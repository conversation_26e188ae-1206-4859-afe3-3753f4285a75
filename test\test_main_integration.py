#!/usr/bin/env python3
"""
Integration test to validate that main.py works with all the fixes.
This test runs the main functionality without requiring full authentication.
"""

import sys
import os
import logging
from datetime import datetime
import tempfile
import shutil

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_main_dry_run():
    """Test main.py functionality in dry run mode."""
    logger.info("Testing main.py integration with all fixes...")
    
    try:
        # Import main modules
        from config_loader import ConfigLoader
        from unified_scanner import UnifiedScanner
        
        # Load config
        config = ConfigLoader("config.yaml")
        logger.info("✓ Configuration loaded successfully")
        
        # Verify pivot point integration is enabled
        assert config.pivot_point_enabled == True, "Pivot point integration should be enabled"
        logger.info("✓ Pivot point integration is enabled")
        
        # Create unified scanner
        scanner = UnifiedScanner("config.yaml")
        logger.info("✓ Unified scanner created successfully")
        
        # Verify scanner components
        assert hasattr(scanner, 'config'), "Scanner should have config"
        assert scanner.config.pivot_point_enabled == True, "Pivot integration should be enabled"
        logger.info("✓ Scanner components verified")
        
        # Test symbol loading (without API calls)
        from universal_symbol_parser import UniversalSymbolParser
        symbol_parser = UniversalSymbolParser(config, config.symbols)
        
        # Test symbol parsing for configured symbols
        symbols = config.symbols  # ['DIXON', 'MCX']
        market_types = config.market_types  # ['OPTIONS']
        
        logger.info(f"Testing with symbols: {symbols}, market types: {market_types}")
        
        # Verify that the symbol parser can handle the configured symbols
        for symbol in symbols:
            for market_type in market_types:
                try:
                    parsed_symbols = symbol_parser.parse_symbols([symbol], market_type)
                    logger.info(f"Symbol parsing successful for {symbol} in {market_type}: {len(parsed_symbols)} symbols")
                except Exception as e:
                    logger.warning(f"Symbol parsing failed for {symbol} in {market_type}: {e}")
        
        logger.info("Main integration test completed successfully")

    except Exception as e:
        logger.error(f"Main integration test failed: {e}")
        assert False, f"Main integration test failed: {e}"

def test_market_data_validation():
    """Test that market data validation fixes are working."""
    logger.info("Testing market data validation fixes...")
    
    try:
        from fyers_client import FyersClient, MarketData
        
        # Create FyersClient instance
        fyers_client = FyersClient()
        
        # Test the fixed OHLC validation
        test_data = {
            'v': {
                'lp': 100.0,
                'volume': 1000,
                'open_price': 98.0,
                'high_price': 0.0,  # Zero value - should trigger fallback
                'low_price': 0.0,   # Zero value - should trigger fallback
                'prev_close_price': 99.0
            }
        }
        
        market_data = fyers_client._parse_market_data("TEST_SYMBOL", test_data)
        
        # Verify fallback logic worked
        assert market_data.high_price == 100.0, f"Expected high_price=100.0, got {market_data.high_price}"
        assert market_data.low_price == 100.0, f"Expected low_price=100.0, got {market_data.low_price}"
        assert market_data.close_price == 99.0, f"Expected close_price=99.0, got {market_data.close_price}"
        
        logger.info("Market data validation fixes are working correctly")

    except Exception as e:
        logger.error(f"Market data validation test failed: {e}")
        assert False, f"Market data validation test failed: {e}"

def test_pivot_point_integration():
    """Test that pivot point integration fixes are working."""
    logger.info("Testing pivot point integration fixes...")
    
    try:
        from config_loader import ConfigLoader
        from pivot_point_integration import PivotPointIntegration
        from fyers_client import FyersClient, MarketData
        
        # Load config
        config = ConfigLoader("config.yaml")
        
        # Create FyersClient
        fyers_client = FyersClient()
        
        # Create pivot point integration
        pivot_integration = PivotPointIntegration(config, fyers_client)
        
        # Verify caching is initialized
        assert hasattr(pivot_integration, '_ohlc_cache'), "Pivot integration should have OHLC cache"
        assert hasattr(pivot_integration, '_failed_symbols'), "Pivot integration should have failed symbols tracking"
        assert len(pivot_integration._ohlc_cache) == 0, "Cache should be initially empty"
        assert len(pivot_integration._failed_symbols) == 0, "Failed symbols should be initially empty"
        
        logger.info("✓ Pivot point integration fixes are working correctly")

    except Exception as e:
        logger.error(f"Pivot point integration test failed: {e}")
        assert False, f"Pivot point integration test failed: {e}"

def main():
    """Run all integration tests."""
    logger.info("=" * 60)
    logger.info("STARTING MAIN INTEGRATION TESTS")
    logger.info("=" * 60)
    
    tests = [
        test_main_dry_run,
        test_market_data_validation,
        test_pivot_point_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    logger.info("=" * 60)
    logger.info(f"INTEGRATION TEST RESULTS: {passed} passed, {failed} failed")
    logger.info("=" * 60)
    
    if failed == 0:
        logger.info("ALL INTEGRATION TESTS PASSED!")
        return True
    else:
        logger.error("SOME INTEGRATION TESTS FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
