# Test Configuration for INDEX only
general:
  env_path: '../.env'
  output_dir: 'reports'
  fyers_api_url: ['https://public.fyers.in/sym_details/NSE_CM.csv', 'https://public.fyers.in/sym_details/NSE_FO.csv']

market_types:
  - INDEX

symbols:
  - 'NIFTY'
  - 'BANKNIFTY'

market_filters:
  min_volume: 10
  max_volume: ***********
  min_ltp_price: 1
  max_ltp_price: 100000.0

timeframe:
  interval: 60
  days_to_fetch: 15

options_filter:
  strike_level: 20
  min_delta: 0.27
  max_delta: 0.64

ce_pe_pairing:
  enabled: false
  min_price_percent: 0.0
  max_price_percent: 2.0

mae_indicator:
  enabled: false
  length: 9
  source: 'close'
  offset: 0
  smoothing_enabled: false
  smoothing_line: 'sma'
  smoothing_length: 9

pivot_point_indicator:
  enabled: false
  calculation_type: 'WEEKLY'
  top_n_closest: 30

rate_limit:
  min_delay_seconds: 0.1
  max_retries: 5
  retry_backoff: 3.0
