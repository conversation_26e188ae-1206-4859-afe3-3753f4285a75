["test/test_fixes_validation.py::test_caching_mechanism", "test/test_fixes_validation.py::test_market_data_validation", "test/test_fixes_validation.py::test_pivot_point_calculation", "test/test_main_functionality.py::test_market_scanners", "test/test_main_functionality.py::test_options_filtering", "test/test_main_functionality.py::test_symbol_parsing", "test/test_main_functionality.py::test_unified_scanner_dry_run", "test/test_main_integration.py::test_main_dry_run", "test/test_main_integration.py::test_market_data_validation", "test/test_main_integration.py::test_pivot_point_integration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_all_symbols_configuration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_market_data_to_filtered_symbol_conversion", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_monthly_expiry_configuration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_pivot_point_calculations", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_pivot_point_filtering_integration", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_symbol_parsing_edge_cases", "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_weekly_symbol_parsing", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_csv_generation_includes_delta_values", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_delta_based_filtering_creates_delta_map", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_delta_filtering_error_logging", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_delta_values_preserved_in_filtered_symbols", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_invalid_delta_values_handling", "test/test_options_delta_values.py::TestOptionsDeltaValues::test_missing_delta_columns_handling", "test/test_options_prefiltering.py::test_options_prefiltering", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_batch_processing_optimization", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_cache_cleanup_functionality", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_fyers_connect_caching", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_memory_optimization_large_dataset", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_option_chain_caching", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_performance_regression_prevention", "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_symbol_parsing_cache_performance", "test/test_pivot_calculation_types.py::test_pivot_calculation_types", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_handles_no_spot_price", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_preserves_ce_pe_balance", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_reduces_symbols_correctly", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_respects_strike_level_config", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_with_empty_symbols", "test/test_timeframe.py::test_fetch_intraday_data"]