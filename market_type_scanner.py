"""
Market Type Scanner for Multi-Market Type Scanner.
Handles scanning of different market types (EQUITY, INDEX, FUTURES, OPTIONS).
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from collections import defaultdict

from config_loader import Config<PERSON>oader
from universal_symbol_parser import UniversalSymbolParser, UniversalSymbol
from options_chain_filter import OptionsChainFilter
from fyers_client import FyersClient
from technical_indicators import MAEAnaly<PERSON>
from pivot_point_integration import PivotPointIntegration, PivotPointData

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Market data for a symbol."""
    symbol: str
    ltp: float
    volume: int
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    prev_close: float = 0.0
    change: float = 0.0
    change_percent: float = 0.0

@dataclass
class FilteredSymbol:
    """Filtered symbol with market data and metadata."""
    symbol: str
    underlying: str
    market_type: str
    market_data: MarketData

    # Market type specific fields
    suffix: Optional[str] = None
    expiry_year: Optional[str] = None
    expiry_month: Optional[str] = None
    strike_price: Optional[float] = None
    option_type: Optional[str] = None
    pair_id: Optional[str] = None  # For CE/PE pairing


class BaseMarketScanner(ABC):
    """Base class for market type scanners."""
    
    def __init__(self, config: ConfigLoader, market_type: str):
        """
        Initialize the base scanner.

        Args:
            config: Configuration loader instance
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
        """
        self.config = config
        self.market_type = market_type
        self.fyers_client = None
        self.pivot_integration = None

        # Initialize symbol parser
        self.symbol_parser = UniversalSymbolParser(config, config.symbols)

        # Performance optimization: Cache for parsed symbols
        self._parsed_symbol_cache = {}

        logger.info(f"Initialized {market_type} scanner")
    
    def authenticate_fyers(self) -> bool:
        """Authenticate with Fyers API."""
        try:
            self.fyers_client = FyersClient(self.config.env_path)
            authenticated = self.fyers_client.authenticate()

            # Initialize pivot point integration if authentication successful
            if authenticated:
                self.pivot_integration = PivotPointIntegration(self.config, self.fyers_client)

            return authenticated
        except Exception as e:
            logger.error(f"Failed to authenticate with Fyers: {e}")
            return False
    
    def get_symbols_for_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols for scanning for this market type."""
        return self.symbol_parser.get_symbols_for_market_type(
            self.market_type, underlying_symbols
        )
    
    def fetch_market_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """Fetch market data for symbols with performance optimization."""
        if not self.fyers_client:
            logger.error("Fyers client not authenticated")
            return {}

        try:
            # Performance optimization: Use optimized method for large symbol lists
            if len(symbols) > 10000:
                logger.info(f"Very large symbol set detected: Using aggressive chunked fetching for {len(symbols)} symbols")
                market_data = self.fyers_client.get_quotes_optimized(symbols, chunk_size=1500)
            elif len(symbols) > 5000:
                logger.info(f"Large symbol set detected: Using optimized chunked fetching for {len(symbols)} symbols")
                market_data = self.fyers_client.get_quotes_optimized(symbols, chunk_size=2000)
            elif len(symbols) > 1000:
                logger.info(f"Medium symbol set: Using optimized fetching for {len(symbols)} symbols")
                market_data = self.fyers_client.get_quotes_optimized(symbols, chunk_size=1000)
            else:
                logger.info(f"Standard fetching for {len(symbols)} symbols")
                market_data = self.fyers_client.get_quotes(symbols)

            # Convert to MarketData objects (from fyers_client.MarketData to market_type_scanner.MarketData)
            converted_data = {}
            for symbol, fyers_data in market_data.items():
                converted_data[symbol] = MarketData(
                    symbol=symbol,
                    ltp=fyers_data.ltp,
                    volume=fyers_data.volume,
                    open_price=fyers_data.open_price,
                    high_price=fyers_data.high_price,
                    low_price=fyers_data.low_price,
                    close_price=fyers_data.close_price,
                    prev_close=fyers_data.prev_close,
                    change=fyers_data.change,
                    change_percent=fyers_data.change_percent
                )

            return converted_data
            
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            return {}

    def _optimize_memory_usage(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize memory usage for large datasets by removing unnecessary data.

        Args:
            data: Dictionary of market data

        Returns:
            Optimized dictionary with reduced memory footprint
        """
        if len(data) < 5000:
            return data  # No optimization needed for small datasets

        # For large datasets, keep only essential fields and optimize memory usage
        optimized_data = {}
        essential_fields = ['symbol', 'ltp', 'volume', 'open', 'high', 'low', 'close', 'prev_close', 'change', 'change_percent']

        for symbol, market_data in data.items():
            if hasattr(market_data, '__dict__'):
                # Create a new MarketData object with only essential fields
                optimized_market_data = MarketData(
                    symbol=symbol,
                    ltp=getattr(market_data, 'ltp', 0.0),
                    volume=getattr(market_data, 'volume', 0),
                    open_price=getattr(market_data, 'open_price', 0.0),
                    high_price=getattr(market_data, 'high_price', 0.0),
                    low_price=getattr(market_data, 'low_price', 0.0),
                    close_price=getattr(market_data, 'close_price', 0.0),
                    prev_close=getattr(market_data, 'prev_close', 0.0),
                    change=getattr(market_data, 'change', 0.0),
                    change_percent=getattr(market_data, 'change_percent', 0.0)
                )
                optimized_data[symbol] = optimized_market_data
            else:
                optimized_data[symbol] = market_data

        # Force garbage collection for large datasets
        if len(data) > 5000:
            import gc
            gc.collect()
            logger.debug(f"Memory optimization: Forced garbage collection for {len(data)} symbols")

        logger.info(f"Memory optimization: Reduced data footprint for {len(data)} symbols")
        return optimized_data

    def apply_volume_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply volume filter. For INDEX market type, volume filtering is disabled."""
        # Skip volume filtering for INDEX market type
        if self.market_type == 'INDEX':
            logger.info(f"Volume filter skipped for {self.market_type} market type")
            return market_data

        filtered_data = {}

        for symbol, data in market_data.items():
            if self.config.min_volume <= data.volume <= self.config.max_volume:
                filtered_data[symbol] = data

        logger.info(f"Volume filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    def apply_ltp_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply LTP filter."""
        filtered_data = {}
        
        for symbol, data in market_data.items():
            if self.config.min_ltp_price <= data.ltp <= self.config.max_ltp_price:
                filtered_data[symbol] = data
        
        logger.info(f"LTP filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    def update_ohlc_with_timeframe_data(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Update OHLC data to use the configured timeframe instead of current session data."""
        try:
            updated_data = {}
            
            for symbol, data in market_data.items():
                try:
                    # Fetch OHLC data for the configured timeframe
                    logger.debug(f"Fetching timeframe OHLC data for {symbol} with interval {self.config.timeframe_interval} and days {self.config.days_to_fetch}")
                    ohlc_data = self.fyers_client.get_historical_data(
                        symbol,
                        self.config.timeframe_interval,
                        self.config.days_to_fetch
                    )

                    if ohlc_data and len(ohlc_data) > 0:
                        # Update OHLC data to use the last candle from historical data (timeframe-specific)
                        last_candle = ohlc_data[-1]
                        data.open_price = last_candle.open
                        data.high_price = last_candle.high
                        data.low_price = last_candle.low
                        data.close_price = last_candle.close
                        
                        # Recalculate change based on timeframe close vs previous close
                        if len(ohlc_data) > 1:
                            prev_candle = ohlc_data[-2]
                            data.prev_close = prev_candle.close
                            data.change = data.close_price - data.prev_close
                            data.change_percent = (data.change / data.prev_close * 100) if data.prev_close > 0 else 0
                        
                        logger.debug(f"Updated OHLC data for {symbol} to use {self.config.timeframe_interval}-minute timeframe data")
                    else:
                        logger.debug(f"No historical data available for {symbol}, keeping quotes data")
                    
                    updated_data[symbol] = data
                    
                except Exception as e:
                    logger.warning(f"Error updating OHLC data for {symbol}: {e}")
                    # Keep original data if there's an error
                    updated_data[symbol] = data

            logger.info(f"Updated OHLC data for {len(updated_data)} symbols to use {self.config.timeframe_interval}-minute timeframe")
            return updated_data
            
        except Exception as e:
            logger.error(f"Error updating OHLC data with timeframe: {e}")
            return market_data

    @abstractmethod
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply market type specific filters."""
        pass
    
    def apply_mae_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply MAE indicator filter if enabled."""
        if not self.config.mae_enabled:
            logger.debug("MAE indicator is disabled, skipping MAE filter")
            return market_data

        try:
            # Initialize MAE analyzer with config settings
            mae_analyzer = MAEAnalyzer(
                length=self.config.mae_length,
                source=self.config.mae_source,
                offset=self.config.mae_offset,
                smoothing_line=self.config.mae_smoothing_line,
                smoothing_length=self.config.mae_smoothing_length
            )

            filtered_data = {}
            mae_passed_count = 0

            for symbol, data in market_data.items():
                try:
                    # Fetch OHLC data for MAE calculation
                    logger.info(f"Fetching OHLC data for {symbol} with interval {self.config.timeframe_interval} and days {self.config.days_to_fetch}")
                    ohlc_data = self.fyers_client.get_historical_data(
                        symbol,
                        self.config.timeframe_interval,
                        self.config.days_to_fetch
                    )

                    if ohlc_data and len(ohlc_data) >= self.config.mae_length:
                        # Calculate MAE and check if price is passing through
                        use_smoothed = getattr(self.config, 'mae_smoothing_enabled', False)
                        is_passing = mae_analyzer.is_price_passing_through_mae(
                            ohlc_data,
                            current_price=data.ltp,
                            use_smoothed=use_smoothed
                        )

                        if is_passing:
                            # Calculate MAE value to attach to symbol
                            mae_default, mae_smoothed = mae_analyzer.calculate_mae(ohlc_data)
                            mae_series = mae_smoothed if use_smoothed else mae_default

                            if not mae_series.empty and not mae_series.isna().iloc[-1]:
                                mae_value = round(mae_series.iloc[-1], 2)
                                # Store MAE value in market data for later use
                                data.mae_value = mae_value
                                
                                filtered_data[symbol] = data
                                mae_passed_count += 1
                                logger.debug(f"MAE filter passed for {symbol}: MAE={mae_value}")
                            else:
                                logger.debug(f"MAE filter failed for {symbol}: invalid MAE value")
                        else:
                            logger.debug(f"MAE filter failed for {symbol}: price not passing through MAE")
                    else:
                        logger.debug(f"MAE filter skipped for {symbol}: insufficient OHLC data")

                except Exception as e:
                    logger.warning(f"Error applying MAE filter to {symbol}: {e}")
                    # Include symbol without MAE filtering if there's an error
                    filtered_data[symbol] = data

            logger.info(f"MAE filter: {mae_passed_count}/{len(market_data)} symbols passed")
            return filtered_data

        except Exception as e:
            logger.error(f"Error in MAE filtering: {e}")
            # Return original data if MAE filtering fails
            return market_data

    def apply_pivot_point_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply pivot point calculations and filtering if enabled."""
        if not self.config.pivot_point_enabled or not self.pivot_integration:
            logger.debug("Pivot point indicator is disabled, skipping pivot point calculations")
            return market_data

        try:
            total_symbols = len(market_data)
            logger.info(f"Applying pivot point calculations to {total_symbols} symbols")

            # Calculate pivot points for each symbol with progress tracking
            pivot_data_map = {}
            processed_count = 0
            successful_count = 0

            for symbol, data in market_data.items():
                processed_count += 1

                # Show progress every 100 symbols or at key milestones
                if processed_count % 100 == 0 or processed_count in [1, 10, 50] or processed_count == total_symbols:
                    progress_pct = (processed_count / total_symbols) * 100
                    logger.info(f"Pivot point calculation progress: {processed_count}/{total_symbols} symbols ({progress_pct:.1f}%) - "
                              f"{successful_count} successful calculations")

                pivot_data = self.pivot_integration.calculate_pivot_points_for_symbol(symbol, data)
                if pivot_data:
                    pivot_data_map[symbol] = pivot_data
                    # Store pivot data in market data for later use
                    data.pivot_data = pivot_data
                    successful_count += 1
                    logger.debug(f"Stored pivot data for {symbol}: distance={pivot_data.distance_to_min_positive_pivot}")
                else:
                    logger.debug(f"No pivot data calculated for {symbol}")

            logger.info(f"Pivot point calculation completed: {successful_count}/{total_symbols} symbols successful "
                       f"({(successful_count/total_symbols)*100:.1f}% success rate)")

            # Debug: Check if pivot data is actually stored in market_data
            symbols_with_pivot_data = 0
            for symbol, data in market_data.items():
                if hasattr(data, 'pivot_data') and data.pivot_data:
                    symbols_with_pivot_data += 1
                    logger.debug(f"Market data for {symbol} has pivot data: distance={data.pivot_data.distance_to_min_positive_pivot}")
                else:
                    logger.debug(f"Market data for {symbol} missing pivot data")

            logger.info(f"Market data contains {symbols_with_pivot_data}/{len(market_data)} symbols with pivot data")
            return market_data

        except Exception as e:
            logger.error(f"Error in pivot point calculations: {e}")
            # Return original data if pivot point calculations fail
            return market_data

    def convert_to_filtered_symbols(self, market_data: Dict[str, MarketData]) -> List[FilteredSymbol]:
        """Convert market data to FilteredSymbol objects."""
        logger.debug(f"convert_to_filtered_symbols called with {len(market_data)} symbols")
        filtered_symbols = []

        try:
            for symbol, data in market_data.items():
                logger.debug(f"Processing symbol {symbol} for conversion")
                # Parse symbol to get metadata with caching
                csv_file = self.config.get_csv_file_for_market_type(self.market_type)

                # Check cache first
                if symbol in self._parsed_symbol_cache:
                    parsed_symbol = self._parsed_symbol_cache[symbol]
                    logger.debug(f"Symbol {symbol}: found in cache")
                else:
                    # For pivot point mode with OPTIONS, use special parsing that bypasses target symbol validation
                    if (self.market_type == 'OPTIONS' and self.config.pivot_point_enabled and
                        hasattr(self, '_delta_map') and symbol in self._delta_map):
                        parsed_symbol = self._parse_symbol_for_pivot_mode(symbol, csv_file)
                        logger.debug(f"Symbol {symbol}: parsed using pivot mode")
                    else:
                        parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
                        logger.debug(f"Symbol {symbol}: parsed using regular mode")

                    # Cache the result
                    self._parsed_symbol_cache[symbol] = parsed_symbol

                logger.debug(f"Symbol {symbol}: parsed_symbol={parsed_symbol is not None}")
                if parsed_symbol:
                    filtered_symbol = FilteredSymbol(
                        symbol=symbol,
                        underlying=parsed_symbol.underlying,
                        market_type=self.market_type,
                        market_data=data,
                        suffix=parsed_symbol.suffix,
                        expiry_year=parsed_symbol.expiry_year,
                        expiry_month=parsed_symbol.expiry_month,
                        strike_price=parsed_symbol.strike_price,
                        option_type=parsed_symbol.option_type
                    )

                    # Add MAE value if available
                    if hasattr(data, 'mae_value'):
                        filtered_symbol.mae_value = data.mae_value

                    # Add pivot data if available
                    has_pivot_data = hasattr(data, 'pivot_data')
                    pivot_data_exists = has_pivot_data and data.pivot_data is not None
                    logger.debug(f"Symbol {symbol}: MarketData has_pivot_data={has_pivot_data}, pivot_data_exists={pivot_data_exists}")

                    if has_pivot_data and data.pivot_data:
                        filtered_symbol.pivot_data = data.pivot_data
                        logger.debug(f"Symbol {symbol}: Transferred pivot data to FilteredSymbol (distance={data.pivot_data.distance_to_min_positive_pivot})")
                    else:
                        logger.debug(f"Symbol {symbol}: No pivot data to transfer")

                    # Add delta data for options (from delta-based filtering)
                    if (self.market_type == 'OPTIONS' and hasattr(self, '_delta_map') and
                        symbol in self._delta_map):
                        delta_value = self._delta_map[symbol]
                        filtered_symbol.delta = delta_value
                        logger.debug(f"Added delta value {delta_value} for symbol {symbol}")
                    else:
                        # For OPTIONS symbols without delta mapping, try to calculate delta
                        if self.market_type == 'OPTIONS' and parsed_symbol:
                            try:
                                from option_utils import black_scholes_delta
                                # Get spot price from market data or fetch it
                                spot_price = getattr(data, 'spot_price', None)
                                if not spot_price and hasattr(self, 'options_filter'):
                                    underlying = parsed_symbol.underlying
                                    spot_prices = self.options_filter.get_spot_prices_for_underlyings([underlying])
                                    spot_price = spot_prices.get(underlying)

                                if spot_price and parsed_symbol.strike_price and parsed_symbol.option_type:
                                    # Calculate delta using Black-Scholes
                                    # Use default values for missing parameters
                                    time_to_expiry = 0.1  # Default 0.1 years (about 36 days)
                                    risk_free_rate = 0.05  # Default 5%
                                    volatility = 0.2  # Default 20%

                                    calculated_delta = black_scholes_delta(
                                        S=spot_price,
                                        K=parsed_symbol.strike_price,
                                        T=time_to_expiry,
                                        r=risk_free_rate,
                                        sigma=volatility,
                                        option_type=parsed_symbol.option_type
                                    )
                                    filtered_symbol.delta = calculated_delta
                                    logger.debug(f"Calculated delta value {calculated_delta} for symbol {symbol}")
                            except Exception as e:
                                logger.debug(f"Could not calculate delta for {symbol}: {e}")
                                filtered_symbol.delta = ''

                    filtered_symbols.append(filtered_symbol)
                    logger.debug(f"Symbol {symbol}: successfully converted to FilteredSymbol")
                else:
                    logger.debug(f"Symbol {symbol}: failed to parse, skipping")

            logger.debug(f"convert_to_filtered_symbols returning {len(filtered_symbols)} symbols")
            return filtered_symbols
        except Exception as e:
            logger.error(f"Exception in convert_to_filtered_symbols: {e}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def _parse_symbol_for_pivot_mode(self, nse_symbol: str, csv_file: str) -> Optional['UniversalSymbol']:
        """
        Parse symbol for pivot point mode, bypassing target symbol validation.
        This allows symbols generated by option chain filtering to be parsed even if
        their underlying is not in the configured target symbols.
        """
        try:
            from universal_symbol_parser import UniversalSymbol

            # Remove NSE: prefix if present
            symbol = nse_symbol.replace('NSE:', '')

            # For OPTIONS, try to parse using the options pattern
            if self.market_type == 'OPTIONS':
                # Monthly options pattern: UNDERLYINGYYMMMSTRIKECE/PE
                import re
                options_pattern = re.compile(r'^([A-Z0-9&]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$')
                match = options_pattern.match(symbol)

                if match:
                    underlying = match.group(1)
                    year = match.group(2)
                    month = match.group(3)
                    strike = float(match.group(4))
                    option_type = match.group(5)

                    return UniversalSymbol(
                        symbol=symbol,
                        market_type='OPTIONS',
                        underlying=underlying,
                        expiry_year=year,
                        expiry_month=month,
                        strike_price=strike,
                        option_type=option_type
                    )

            # Fallback to regular parsing if pattern doesn't match
            return self.symbol_parser.parse_symbol(nse_symbol, csv_file)

        except Exception as e:
            logger.debug(f"Error in pivot mode symbol parsing for {nse_symbol}: {e}")
            return None

    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")
            
            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []
            
            # Get symbols
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)
            
            if not symbols_to_scan:
                logger.warning(f"No {self.market_type} symbols found for scanning")
                return []
            
            logger.info(f"Starting market data fetch for {len(symbols_to_scan)} {self.market_type} symbols")

            # Fetch market data with progress tracking
            market_data = self.fetch_market_data(symbols_to_scan)
            
            if not market_data:
                logger.warning("No market data received")
                return []
            
            # Update OHLC data with timeframe-specific data
            market_data = self.update_ohlc_with_timeframe_data(market_data)
            
            # Apply filters
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)
            filtered_data = self.apply_market_specific_filters(filtered_data)
            filtered_data = self.apply_mae_filter(filtered_data)
            filtered_data = self.apply_pivot_point_filter(filtered_data)

            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)

            # Apply pivot point proximity filtering if enabled
            if self.config.pivot_point_enabled and self.pivot_integration:
                pivot_data_map = {}
                for symbol_obj in filtered_symbols:
                    if hasattr(symbol_obj, 'pivot_data') and symbol_obj.pivot_data:
                        pivot_data_map[symbol_obj.symbol] = symbol_obj.pivot_data

                if pivot_data_map:
                    filtered_symbols = self.pivot_integration.filter_by_closest_pivot_points(
                        filtered_symbols, pivot_data_map
                    )
            
            logger.info(f"{self.market_type} scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []


class EquityScanner(BaseMarketScanner):
    """Scanner for equity symbols."""

    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'EQUITY')

    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method for equity symbols."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")

            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []

            # For EQUITY with pivot point enabled, use pivot point compatible flow
            if self.config.pivot_point_enabled:
                return self._scan_equity_with_pivot_point_flow()

            # Original flow for when pivot points are disabled
            return self._scan_equity_original_flow()

        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []

    def _scan_equity_with_pivot_point_flow(self) -> List[FilteredSymbol]:
        """Scan equity using pivot point compatible flow."""
        try:
            # Get symbols for configured underlyings
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)

            if not symbols_to_scan:
                logger.warning(f"No EQUITY symbols found for pivot point scanning")
                return []

            logger.info(f"Pivot point mode: Fetching market data for {len(symbols_to_scan)} EQUITY symbols")

            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)

            if not market_data:
                logger.warning("No market data received")
                return []

            # Apply basic filters (volume and LTP)
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)

            # Calculate pivot points for all remaining symbols
            filtered_data = self.apply_pivot_point_filter(filtered_data)

            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)

            # Apply pivot point proximity filtering
            if self.pivot_integration:
                pivot_data_map = {}
                for symbol_obj in filtered_symbols:
                    if hasattr(symbol_obj, 'pivot_data') and symbol_obj.pivot_data:
                        pivot_data_map[symbol_obj.symbol] = symbol_obj.pivot_data

                if pivot_data_map:
                    filtered_symbols = self.pivot_integration.filter_by_closest_pivot_points(
                        filtered_symbols, pivot_data_map
                    )

            logger.info(f"EQUITY pivot point scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error during EQUITY pivot point scanning: {e}")
            return []

    def _scan_equity_original_flow(self) -> List[FilteredSymbol]:
        """Original scanning flow for when pivot points are disabled."""
        # Use the base class scan_symbols method
        return super().scan_symbols()

    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply equity-specific filters."""
        # If pivot point integration is enabled, skip additional filtering
        if self.config.pivot_point_enabled:
            logger.info(f"Pivot point mode: Skipping additional equity filtering, using {len(market_data)} symbols")
            return market_data

        # No additional filters for equity beyond volume and LTP
        return market_data


class IndexScanner(BaseMarketScanner):
    """Scanner for index symbols."""

    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'INDEX')

    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method for index symbols."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")

            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []

            # For INDEX with pivot point enabled, use pivot point compatible flow
            if self.config.pivot_point_enabled:
                return self._scan_index_with_pivot_point_flow()

            # Original flow for when pivot points are disabled
            return self._scan_index_original_flow()

        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []

    def _scan_index_with_pivot_point_flow(self) -> List[FilteredSymbol]:
        """Scan index using pivot point compatible flow."""
        try:
            # Get symbols for configured underlyings
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)

            if not symbols_to_scan:
                logger.warning(f"No INDEX symbols found for pivot point scanning")
                return []

            logger.info(f"Pivot point mode: Fetching market data for {len(symbols_to_scan)} INDEX symbols")

            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)

            if not market_data:
                logger.warning("No market data received")
                return []

            # Apply basic filters (volume and LTP)
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)

            # Calculate pivot points for all remaining symbols
            filtered_data = self.apply_pivot_point_filter(filtered_data)

            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)

            # Apply pivot point proximity filtering
            if self.pivot_integration:
                pivot_data_map = {}
                for symbol_obj in filtered_symbols:
                    if hasattr(symbol_obj, 'pivot_data') and symbol_obj.pivot_data:
                        pivot_data_map[symbol_obj.symbol] = symbol_obj.pivot_data

                if pivot_data_map:
                    filtered_symbols = self.pivot_integration.filter_by_closest_pivot_points(
                        filtered_symbols, pivot_data_map
                    )

            logger.info(f"INDEX pivot point scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error during INDEX pivot point scanning: {e}")
            return []

    def _scan_index_original_flow(self) -> List[FilteredSymbol]:
        """Original scanning flow for when pivot points are disabled."""
        # Use the base class scan_symbols method
        return super().scan_symbols()

    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply index-specific filters."""
        # If pivot point integration is enabled, skip additional filtering
        if self.config.pivot_point_enabled:
            logger.info(f"Pivot point mode: Skipping additional index filtering, using {len(market_data)} symbols")
            return market_data

        # No additional filters for index beyond volume and LTP
        return market_data


class FuturesScanner(BaseMarketScanner):
    """Scanner for futures symbols."""

    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'FUTURES')

    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method for futures symbols."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")

            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []

            # For FUTURES with pivot point enabled, use pivot point compatible flow
            if self.config.pivot_point_enabled:
                return self._scan_futures_with_pivot_point_flow()

            # Original flow for when pivot points are disabled
            return self._scan_futures_original_flow()

        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []

    def _scan_futures_with_pivot_point_flow(self) -> List[FilteredSymbol]:
        """Scan futures using pivot point compatible flow."""
        try:
            # Get symbols for configured underlyings
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)

            if not symbols_to_scan:
                logger.warning(f"No FUTURES symbols found for pivot point scanning")
                return []

            logger.info(f"Pivot point mode: Fetching market data for {len(symbols_to_scan)} FUTURES symbols")

            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)

            if not market_data:
                logger.warning("No market data received")
                return []

            # Apply basic filters (volume and LTP)
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)

            # Calculate pivot points for all remaining symbols
            filtered_data = self.apply_pivot_point_filter(filtered_data)

            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)

            # Apply pivot point proximity filtering
            if self.pivot_integration:
                pivot_data_map = {}
                for symbol_obj in filtered_symbols:
                    if hasattr(symbol_obj, 'pivot_data') and symbol_obj.pivot_data:
                        pivot_data_map[symbol_obj.symbol] = symbol_obj.pivot_data

                if pivot_data_map:
                    filtered_symbols = self.pivot_integration.filter_by_closest_pivot_points(
                        filtered_symbols, pivot_data_map
                    )

            logger.info(f"FUTURES pivot point scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error during FUTURES pivot point scanning: {e}")
            return []

    def _scan_futures_original_flow(self) -> List[FilteredSymbol]:
        """Original scanning flow for when pivot points are disabled."""
        # Use the base class scan_symbols method
        return super().scan_symbols()

    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply futures-specific filters."""
        # If pivot point integration is enabled, skip additional filtering
        if self.config.pivot_point_enabled:
            logger.info(f"Pivot point mode: Skipping additional futures filtering, using {len(market_data)} symbols")
            return market_data

        # No additional filters for futures beyond volume and LTP
        return market_data


class OptionsScanner(BaseMarketScanner):
    """Scanner for options symbols."""

    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'OPTIONS')
        self.options_filter = OptionsChainFilter(config)

    def get_symbols_for_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols for scanning with pre-filtering for options to reduce API calls."""
        try:
            # If pivot point integration is enabled, use pivot_point_core.py compatible filtering
            if self.config.pivot_point_enabled:
                return self._get_symbols_for_pivot_point_scanning(underlying_symbols)

            # Original logic for non-pivot point scanning
            all_options_symbols = self.symbol_parser.get_symbols_for_market_type(
                self.market_type, underlying_symbols
            )

            logger.info(f"Loaded {len(all_options_symbols)} raw OPTIONS symbols")

            # Apply pre-filtering to reduce symbols before API calls
            if len(all_options_symbols) > 10000:  # Only pre-filter if we have too many symbols
                filtered_symbols = self._apply_pre_filtering(all_options_symbols, underlying_symbols)
                logger.info(f"Pre-filtering reduced symbols from {len(all_options_symbols)} to {len(filtered_symbols)}")
                return filtered_symbols
            else:
                return all_options_symbols

        except Exception as e:
            logger.error(f"Error in get_symbols_for_scanning: {e}")
            return []

    def _get_symbols_for_pivot_point_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols using pivot_point_core.py compatible approach - delta-based filtering."""
        try:
            # Handle 'ALL' symbol case by using CSV-based approach like CE PE Pairing and MAE indicator
            if underlying_symbols is None and 'ALL' in self.config.symbols:
                logger.info("Pivot point mode: 'ALL' symbol detected, using CSV-based symbol loading")
                # Use the same CSV-based approach as other indicators
                all_options_symbols = self.symbol_parser.get_symbols_for_market_type(
                    self.market_type, None  # None means use all symbols from CSV
                )

                logger.info(f"Pivot point mode: Loaded {len(all_options_symbols)} OPTIONS symbols from NSE_FO.csv")

                # Always apply pre-filtering to reduce symbols before delta-based filtering
                # This ensures consistency with CE PE pairing and MAE indicator behavior
                filtered_symbols = self._apply_pre_filtering(all_options_symbols, None)
                logger.info(f"📈 Pivot Point Analysis: Processing {len(filtered_symbols)} OPTIONS symbols (reduced from {len(all_options_symbols)})")
                return filtered_symbols

            # Reuse existing authenticated fyers_client to avoid duplicate authentication
            # Convert fyers_client to fyers_connect format for compatibility
            from fyers_connect import FyersConnect
            from fyers_config import FyersConfig

            # Create FyersConnect instance but reuse existing authentication
            fyers_config = FyersConfig(env_path=self.config.env_path)
            fyers_client = FyersConnect(fyers_config)

            # Reuse existing authentication from self.fyers_client
            if self.fyers_client and self.fyers_client.is_authenticated():
                # Copy authentication details to avoid duplicate login
                fyers_client.access_token = self.fyers_client.access_token
                fyers_client.fyers = self.fyers_client.fyers_api
                logger.info("Reusing existing Fyers authentication for pivot point scanning")
            else:
                # Fallback: authenticate if not already done
                if not fyers_client.login():
                    logger.error("Fyers login failed for pivot point scanning")
                    return []

            # Get configured symbols to process (same as pivot_point_core.py)
            symbols_to_process = underlying_symbols if underlying_symbols else self.config.symbols
            logger.info(f"Pivot point mode: Processing specific symbols: {symbols_to_process}")

            all_filtered_symbols = []

            for symbol in symbols_to_process:
                try:
                    # Skip 'ALL' in individual processing
                    if symbol == 'ALL':
                        continue

                    # Get symbol configuration (same as pivot_point_core.py)
                    symbol_config_data = self.config.get_symbol_config(symbol)
                    num_strikes = symbol_config_data.get("num_strikes_each_side", 10)

                    # Fetch option chain using configured expiry type
                    expiry_type = self.config.options_expiry_type
                    option_chain = fyers_client.get_option_chain(symbol, expiry_type=expiry_type, num_strikes_each_side=num_strikes)

                    if not option_chain:
                        logger.warning(f"No option chain data fetched for {symbol}")
                        continue

                    # Apply delta-based filtering (same as pivot_point_core.py)
                    filtered_options = self._apply_delta_based_filtering(option_chain, fyers_client)

                    if filtered_options:
                        all_filtered_symbols.extend(filtered_options)
                        logger.info(f"Pivot point mode: Added {len(filtered_options)} options for {symbol}")

                except Exception as e:
                    logger.warning(f"Skipping symbol {symbol} in pivot point mode: {e}")
                    # Continue processing other symbols instead of failing completely
                    continue

            logger.info(f"Pivot point mode: Total symbols selected: {len(all_filtered_symbols)}")
            return all_filtered_symbols

        except Exception as e:
            logger.error(f"Error in pivot point compatible symbol selection: {e}")
            return []

    def _apply_delta_based_filtering(self, option_chain: List[dict], fyers_client) -> List[str]:
        """Apply delta-based filtering exactly like pivot_point_core.py get_filtered_options."""
        try:
            import pandas as pd

            # Convert to DataFrame (same as pivot_point_core.py)
            calls_df = pd.DataFrame(option_chain)
            puts_df = calls_df.copy()

            logger.debug(f"Option chain columns: {list(calls_df.columns)}")
            logger.debug(f"Option chain shape: {calls_df.shape}")

            # Check if required columns exist
            if 'call_delta' not in calls_df.columns or 'put_delta' not in calls_df.columns:
                logger.warning(f"Delta columns missing in option chain. Available columns: {list(calls_df.columns)}")
                return []

            # Get spot price for ATM analysis
            spot_price = calls_df['spot_price'].iloc[0] if 'spot_price' in calls_df.columns and len(calls_df) > 0 else None

            # Filter by delta range (same as pivot_point_core.py)
            calls_filtered = calls_df[calls_df['call_delta'].between(self.config.min_delta, self.config.max_delta)].copy()
            puts_filtered = puts_df[puts_df['put_delta'].abs().between(self.config.min_delta, self.config.max_delta)].copy()

            logger.debug(f"Delta filtering: {len(calls_filtered)} calls, {len(puts_filtered)} puts from {len(calls_df)} total options")

            # Additionally include ATM symbols even if they're slightly outside delta range
            if spot_price is not None:
                # Find ATM strikes (closest to spot price) - check available column names
                strike_col = None
                for col_name in ['strike', 'strike_price', 'Strike', 'Strike_Price']:
                    if col_name in calls_df.columns:
                        strike_col = col_name
                        break

                if strike_col is None:
                    logger.warning("No strike price column found in option chain data. Available columns: " + str(list(calls_df.columns)))
                    # Continue without ATM filtering
                else:
                    calls_df['distance_from_atm'] = abs(calls_df[strike_col] - spot_price)
                    puts_df['distance_from_atm'] = abs(puts_df[strike_col] - spot_price)

                    # Get the 2 closest strikes for calls and puts (to ensure we capture ATM)
                    atm_calls = calls_df.nsmallest(2, 'distance_from_atm')
                    atm_puts = puts_df.nsmallest(2, 'distance_from_atm')

                    # Add ATM symbols that might be missing from delta filtering
                    atm_calls_to_add = atm_calls[~atm_calls.index.isin(calls_filtered.index)]
                    atm_puts_to_add = atm_puts[~atm_puts.index.isin(puts_filtered.index)]

                    if len(atm_calls_to_add) > 0:
                        logger.info(f"Adding {len(atm_calls_to_add)} ATM call options that were outside delta range")
                        calls_filtered = pd.concat([calls_filtered, atm_calls_to_add], ignore_index=True)

                    if len(atm_puts_to_add) > 0:
                        logger.info(f"Adding {len(atm_puts_to_add)} ATM put options that were outside delta range")
                        puts_filtered = pd.concat([puts_filtered, atm_puts_to_add], ignore_index=True)

            # Use the filtered dataframes
            calls_df = calls_filtered
            puts_df = puts_filtered

            # Assign type (same as pivot_point_core.py)
            calls_df['type'] = 'CE'
            puts_df['type'] = 'PE'

            # Store delta information for later use
            self._delta_map = {}

            # Prepare symbol lists and store delta values
            filtered_symbols = []

            # Add call options with delta values
            for _, row in calls_df.iterrows():
                if 'call_symbol' in row and pd.notna(row['call_symbol']):
                    symbol = row['call_symbol']
                    delta_value = row.get('call_delta', '')
                    # Ensure delta value is numeric
                    if pd.notna(delta_value) and delta_value != '':
                        try:
                            delta_value = float(delta_value)
                        except (ValueError, TypeError):
                            logger.debug(f"Invalid delta value for {symbol}: {delta_value}")
                            delta_value = ''
                    filtered_symbols.append(symbol)
                    self._delta_map[symbol] = delta_value
                    logger.debug(f"Mapped CE delta {delta_value} for symbol {symbol}")

            # Add put options with delta values
            for _, row in puts_df.iterrows():
                if 'put_symbol' in row and pd.notna(row['put_symbol']):
                    symbol = row['put_symbol']
                    delta_value = row.get('put_delta', '')
                    # Ensure delta value is numeric
                    if pd.notna(delta_value) and delta_value != '':
                        try:
                            delta_value = float(delta_value)
                        except (ValueError, TypeError):
                            logger.debug(f"Invalid delta value for {symbol}: {delta_value}")
                            delta_value = ''
                    filtered_symbols.append(symbol)
                    self._delta_map[symbol] = delta_value
                    logger.debug(f"Mapped PE delta {delta_value} for symbol {symbol}")

            logger.info(f"Delta-based filtering: Created delta map with {len(self._delta_map)} entries")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error in delta-based filtering: {e}")
            return []

    def _apply_pre_filtering(self, symbols: List[str], underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Apply pre-filtering to options symbols based on spot prices and strike ranges."""
        try:
            # Parse symbols to UniversalSymbol objects
            csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
            parsed_symbols = []

            for symbol in symbols:
                parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
                if parsed_symbol and parsed_symbol.is_options():
                    parsed_symbols.append(parsed_symbol)

            logger.info(f"Parsed {len(parsed_symbols)} valid options symbols for pre-filtering")

            # Get unique underlying symbols
            unique_underlyings = list(set(s.underlying for s in parsed_symbols))
            logger.info(f"Found {len(unique_underlyings)} unique underlying symbols")

            # Fetch spot prices for underlying symbols
            spot_prices = self.options_filter.get_spot_prices_for_underlyings(unique_underlyings)
            # Remove this redundant log message as it's already logged in options_filter

            # Apply aggressive pre-filtering to reduce symbols significantly
            max_symbols_per_underlying = 150  # Limit symbols per underlying for API efficiency
            filtered_symbols = self.options_filter.pre_filter_options_symbols(
                parsed_symbols, spot_prices, max_symbols_per_underlying
            )

            # Convert back to NSE symbol strings
            filtered_symbol_strings = [s.get_nse_symbol() for s in filtered_symbols]

            logger.info(f"🔍 Pre-filtering completed: {len(filtered_symbol_strings)}/{len(symbols)} symbols selected for market data fetch")
            return filtered_symbol_strings

        except Exception as e:
            logger.error(f"Error in pre-filtering: {e}")
            # Return original symbols if pre-filtering fails
            return symbols

    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply options-specific filters including CE/PE pairing."""
        # If pivot point integration is enabled, skip additional filtering
        # since we already applied delta-based filtering
        if self.config.pivot_point_enabled:
            logger.info(f"Pivot point mode: Skipping additional options filtering, using {len(market_data)} symbols")
            return market_data

        # Original logic for non-pivot point scanning
        # Since we already applied pre-filtering, we can apply lighter post-filtering here
        # Convert market data to UniversalSymbol objects for final options filtering
        symbols = []
        csv_file = self.config.get_csv_file_for_market_type('OPTIONS')

        for symbol in market_data.keys():
            parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
            if parsed_symbol:
                symbols.append(parsed_symbol)

        # Apply final options chain filtering (this should be much lighter now)
        filtered_symbols = self.options_filter.filter_options_symbols(symbols)

        # Convert back to market data dictionary
        filtered_data = {}
        filtered_symbol_names = {s.get_nse_symbol() for s in filtered_symbols}

        for symbol, data in market_data.items():
            if symbol in filtered_symbol_names:
                filtered_data[symbol] = data

        logger.info(f"Options chain filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data

    def apply_ce_pe_pairing_on_filtered_symbols(self, filtered_symbols: List[FilteredSymbol]) -> List[FilteredSymbol]:
        """
        Apply CE/PE pairing filter on FilteredSymbol objects that have market data.
        Pairs by underlying, expiry_year, expiry_month, and closest LTP.
        Pair ID format: UnderlyingSymbolYYMMM_PAIRID (e.g., BANKNIFTY25JUL_0001)
        """
        if not self.config.ce_pe_pairing_enabled:
            logger.debug("CE/PE pairing is disabled, skipping pairing filter")
            return filtered_symbols

        min_price_percent = self.config.ce_pe_min_price_percent
        max_price_percent = self.config.ce_pe_max_price_percent

        # Group options by (underlying, expiry_year, expiry_month)
        expiry_groups = defaultdict(lambda: {'CE': [], 'PE': []})
        for symbol in filtered_symbols:
            if symbol.market_type == 'OPTIONS' and symbol.option_type in ['CE', 'PE']:
                key = (symbol.underlying, symbol.expiry_year, symbol.expiry_month)
                expiry_groups[key][symbol.option_type].append(symbol)

        paired_symbols = []
        pair_id_counter = 1
        for (underlying, expiry_year, expiry_month), group in expiry_groups.items():
            ce_list = group['CE']
            pe_list = group['PE']
            # Sort both lists by LTP for deterministic pairing
            ce_list = [s for s in ce_list if s.market_data.ltp > 0]
            pe_list = [s for s in pe_list if s.market_data.ltp > 0]
            ce_list.sort(key=lambda s: s.market_data.ltp)
            pe_list.sort(key=lambda s: s.market_data.ltp)
            used_pe = set()
            for ce in ce_list:
                # Find PE with closest LTP
                closest_pe = None
                min_diff = float('inf')
                for pe in pe_list:
                    if pe.symbol in used_pe:
                        continue
                    price_diff_percent = abs(ce.market_data.ltp - pe.market_data.ltp) / max(ce.market_data.ltp, pe.market_data.ltp) * 100
                    if min_price_percent <= price_diff_percent <= max_price_percent:
                        diff = abs(ce.market_data.ltp - pe.market_data.ltp)
                        if diff < min_diff:
                            min_diff = diff
                            closest_pe = pe
                if closest_pe:
                    # Assign pair ID in required format: UnderlyingSymbolYYMMM_PAIRID
                    yy = str(expiry_year)[-2:] if expiry_year else ''
                    pair_id = f"{underlying}{yy}{expiry_month}_" + f"{pair_id_counter:04d}"
                    ce.pair_id = pair_id
                    closest_pe.pair_id = pair_id
                    paired_symbols.extend([ce, closest_pe])
                    used_pe.add(closest_pe.symbol)
                    pair_id_counter += 1
                    logger.debug(f"Paired {ce.symbol} and {closest_pe.symbol} with pair_id {pair_id} (LTP diff: {min_diff:.2f})")
        logger.info(f"CE/PE pairing filter: {len(paired_symbols)}/{len(filtered_symbols)} symbols paired ({len(paired_symbols)//2} pairs created)")
        return paired_symbols

    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method with CE/PE pairing applied after market data is available."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")

            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []

            # For OPTIONS with pivot point enabled, use pivot_point_core.py compatible flow
            if self.config.pivot_point_enabled:
                return self._scan_options_with_pivot_point_flow()

            # Original flow for when pivot points are disabled
            return self._scan_options_original_flow()

        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []

    def _scan_options_with_pivot_point_flow(self) -> List[FilteredSymbol]:
        """Scan options using pivot_point_core.py compatible flow."""
        try:
            # Get symbols using delta-based filtering (already done in get_symbols_for_scanning)
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)

            if not symbols_to_scan:
                logger.warning(f"No OPTIONS symbols found for pivot point scanning")
                return []

            logger.info(f"Pivot point mode: Fetching market data for {len(symbols_to_scan)} OPTIONS symbols")

            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)

            if not market_data:
                logger.warning("No market data received")
                return []

            # Apply only basic filters (volume and LTP) - skip options chain filtering
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)

            # Calculate pivot points for all remaining symbols
            filtered_data = self.apply_pivot_point_filter(filtered_data)
            logger.debug(f"After pivot point calculations: {len(filtered_data)} symbols remain")
            logger.debug(f"Filtered data keys: {list(filtered_data.keys()) if filtered_data else 'None'}")

            # Convert to FilteredSymbol objects
            logger.debug(f"About to call convert_to_filtered_symbols with {len(filtered_data)} symbols")
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)
            logger.debug(f"After convert_to_filtered_symbols: {len(filtered_symbols)} symbols remain")

            # Apply pivot point proximity filtering (same as pivot_point_core.py top N selection)
            if self.pivot_integration:
                pivot_data_map = {}
                logger.debug(f"Building pivot data map from {len(filtered_symbols)} filtered symbols")

                for symbol_obj in filtered_symbols:
                    symbol = symbol_obj.symbol
                    has_pivot_attr = hasattr(symbol_obj, 'pivot_data')
                    pivot_data_exists = has_pivot_attr and symbol_obj.pivot_data is not None

                    logger.debug(f"Symbol {symbol}: has_pivot_data_attr={has_pivot_attr}, pivot_data_exists={pivot_data_exists}")

                    if has_pivot_attr and symbol_obj.pivot_data:
                        pivot_data_map[symbol] = symbol_obj.pivot_data
                        logger.debug(f"Added {symbol} to pivot_data_map with distance={symbol_obj.pivot_data.distance_to_min_positive_pivot}")

                logger.info(f"Built pivot data map with {len(pivot_data_map)} entries from {len(filtered_symbols)} symbols")

                if pivot_data_map:
                    filtered_symbols = self.pivot_integration.filter_by_closest_pivot_points(
                        filtered_symbols, pivot_data_map
                    )
                else:
                    logger.warning("Pivot data map is empty - no symbols will pass pivot point filtering")

            logger.info(f"OPTIONS pivot point scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error during OPTIONS pivot point scanning: {e}")
            return []

    def _scan_options_original_flow(self) -> List[FilteredSymbol]:
        """Original scanning flow for when pivot points are disabled."""
        try:
            # Get symbols
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)

            if not symbols_to_scan:
                logger.warning(f"No {self.market_type} symbols found for scanning")
                return []

            logger.info(f"Fetching market data for {len(symbols_to_scan)} {self.market_type} symbols")

            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)

            if not market_data:
                logger.warning("No market data received")
                return []

            # Apply filters
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)
            filtered_data = self.apply_market_specific_filters(filtered_data)
            filtered_data = self.apply_mae_filter(filtered_data)
            filtered_data = self.apply_pivot_point_filter(filtered_data)

            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)

            # Apply CE/PE pairing filter if enabled (after market data is available)
            if self.config.ce_pe_pairing_enabled:
                filtered_symbols = self.apply_ce_pe_pairing_on_filtered_symbols(filtered_symbols)

            # Apply pivot point proximity filtering if enabled
            if self.config.pivot_point_enabled and self.pivot_integration:
                pivot_data_map = {}
                for symbol_obj in filtered_symbols:
                    if hasattr(symbol_obj, 'pivot_data') and symbol_obj.pivot_data:
                        pivot_data_map[symbol_obj.symbol] = symbol_obj.pivot_data

                if pivot_data_map:
                    filtered_symbols = self.pivot_integration.filter_by_closest_pivot_points(
                        filtered_symbols, pivot_data_map
                    )

            logger.info(f"{self.market_type} scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []


class MarketTypeScannerFactory:
    """Factory for creating market type scanners."""
    
    @staticmethod
    def create_scanner(market_type: str, config: ConfigLoader) -> BaseMarketScanner:
        """
        Create a scanner for the specified market type.
        
        Args:
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
            config: Configuration loader instance
            
        Returns:
            Appropriate scanner instance
        """
        if market_type == 'EQUITY':
            return EquityScanner(config)
        elif market_type == 'INDEX':
            return IndexScanner(config)
        elif market_type == 'FUTURES':
            return FuturesScanner(config)
        elif market_type == 'OPTIONS':
            return OptionsScanner(config)
        else:
            raise ValueError(f"Unknown market type: {market_type}")
    
    @staticmethod
    def get_supported_market_types() -> List[str]:
        """Get list of supported market types."""
        return ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']

