"""
Test suite for OPTIONS delta values capture and CSV generation.
This test ensures that delta values are properly captured and written to CSV files.
"""

import pytest
import pandas as pd
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Import the modules we need to test
from config_loader import ConfigLoader
from market_type_scanner import OptionsScanner
from report_generator import ReportGenerator


class TestOptionsDeltaValues:
    """Test class for OPTIONS delta values functionality."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=ConfigLoader)
        config.pivot_point_enabled = True
        config.min_delta = 0.27
        config.max_delta = 0.64
        config.symbols = ['NIFTY']
        config.env_path = '../.env'
        config.get_symbol_config.return_value = {"num_strikes_each_side": 10}
        config.get_csv_file_for_market_type.return_value = 'NSE_FO.csv'
        return config

    @pytest.fixture
    def sample_option_chain(self):
        """Create sample option chain data with delta values."""
        return [
            {
                'call_symbol': 'NSE:NIFTY25JUL24000CE',
                'put_symbol': 'NSE:NIFTY25JUL24000PE',
                'call_delta': 0.45,
                'put_delta': -0.55,
                'strike': 24000,
                'spot_price': 24100,
                'expiry_date': '2025-07-31'
            },
            {
                'call_symbol': 'NSE:NIFTY25JUL24100CE',
                'put_symbol': 'NSE:NIFTY25JUL24100PE',
                'call_delta': 0.52,
                'put_delta': -0.48,
                'strike': 24100,
                'spot_price': 24100,
                'expiry_date': '2025-07-31'
            },
            {
                'call_symbol': 'NSE:NIFTY25JUL24200CE',
                'put_symbol': 'NSE:NIFTY25JUL24200PE',
                'call_delta': 0.35,
                'put_delta': -0.65,
                'strike': 24200,
                'spot_price': 24100,
                'expiry_date': '2025-07-31'
            }
        ]

    @pytest.fixture
    def sample_market_data(self):
        """Create sample market data for testing."""
        from market_type_scanner import MarketData
        return {
            'NSE:NIFTY25JUL24000CE': MarketData(
                symbol='NSE:NIFTY25JUL24000CE',
                ltp=150.0,
                volume=100000,
                open_price=145.0,
                high_price=155.0,
                low_price=140.0,
                close_price=150.0,
                prev_close=148.0,
                change=2.0,
                change_percent=1.35
            ),
            'NSE:NIFTY25JUL24000PE': MarketData(
                symbol='NSE:NIFTY25JUL24000PE',
                ltp=50.0,
                volume=80000,
                open_price=52.0,
                high_price=55.0,
                low_price=48.0,
                close_price=50.0,
                prev_close=51.0,
                change=-1.0,
                change_percent=-1.96
            ),
            'NSE:NIFTY25JUL24100CE': MarketData(
                symbol='NSE:NIFTY25JUL24100CE',
                ltp=100.0,
                volume=120000,
                open_price=98.0,
                high_price=105.0,
                low_price=95.0,
                close_price=100.0,
                prev_close=99.0,
                change=1.0,
                change_percent=1.01
            ),
            'NSE:NIFTY25JUL24100PE': MarketData(
                symbol='NSE:NIFTY25JUL24100PE',
                ltp=100.0,
                volume=110000,
                open_price=102.0,
                high_price=105.0,
                low_price=98.0,
                close_price=100.0,
                prev_close=101.0,
                change=-1.0,
                change_percent=-0.99
            )
        }

    def test_delta_based_filtering_creates_delta_map(self, mock_config, sample_option_chain):
        """Test that delta-based filtering creates a proper delta map."""
        scanner = OptionsScanner(mock_config)
        
        # Mock the fyers client
        mock_fyers_client = Mock()
        
        # Call the delta-based filtering method
        filtered_symbols = scanner._apply_delta_based_filtering(sample_option_chain, mock_fyers_client)
        
        # Check that symbols were filtered
        assert len(filtered_symbols) > 0
        
        # Check that delta map was created
        assert hasattr(scanner, '_delta_map')
        assert len(scanner._delta_map) > 0
        
        # Check that delta values are properly stored
        for symbol in filtered_symbols:
            assert symbol in scanner._delta_map
            delta_value = scanner._delta_map[symbol]
            # Delta value should be either a float or empty string
            assert isinstance(delta_value, (float, str))
            if isinstance(delta_value, float):
                assert -1.0 <= delta_value <= 1.0  # Delta should be between -1 and 1

    def test_delta_values_preserved_in_filtered_symbols(self, mock_config, sample_option_chain, sample_market_data):
        """Test that delta values are preserved when converting to FilteredSymbol objects."""
        scanner = OptionsScanner(mock_config)
        
        # Mock the symbol parser
        mock_symbol_parser = Mock()
        mock_parsed_symbol = Mock()
        mock_parsed_symbol.underlying = 'NIFTY'
        mock_parsed_symbol.strike_price = 24000
        mock_parsed_symbol.option_type = 'CE'
        mock_parsed_symbol.expiry_year = '25'
        mock_parsed_symbol.expiry_month = 'JUL'
        mock_symbol_parser.parse_symbol.return_value = mock_parsed_symbol
        scanner.symbol_parser = mock_symbol_parser
        
        # Create delta map first
        scanner._delta_map = {
            'NSE:NIFTY25JUL24000CE': 0.45,
            'NSE:NIFTY25JUL24000PE': -0.55,
            'NSE:NIFTY25JUL24100CE': 0.52,
            'NSE:NIFTY25JUL24100PE': -0.48
        }
        
        # Convert to filtered symbols
        filtered_symbols = scanner.convert_to_filtered_symbols(sample_market_data)
        
        # Check that delta values are preserved
        for symbol in filtered_symbols:
            if symbol.symbol in scanner._delta_map:
                assert hasattr(symbol, 'delta')
                expected_delta = scanner._delta_map[symbol.symbol]
                assert symbol.delta == expected_delta

    def test_csv_generation_includes_delta_values(self, mock_config):
        """Test that CSV generation includes delta values in the output."""
        from market_type_scanner import FilteredSymbol, MarketData

        # Create test data with delta values
        market_data = MarketData(
            symbol='NSE:NIFTY25JUL24000CE',
            ltp=150.0,
            volume=100000
        )

        filtered_symbol = FilteredSymbol(
            symbol='NSE:NIFTY25JUL24000CE',
            underlying='NIFTY',
            market_type='OPTIONS',
            market_data=market_data,
            strike_price=24000,
            option_type='CE',
            expiry_year='25',
            expiry_month='JUL'
        )

        # Add delta value
        filtered_symbol.delta = 0.45

        # Create temporary directory for test output
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create report generator with proper output directory
            report_generator = ReportGenerator(temp_dir, mock_config)

            # Generate CSV report
            csv_file = report_generator.create_csv_report([filtered_symbol])

            # Read the CSV and check for delta column
            df = pd.read_csv(csv_file)

            # Check that delta column exists
            assert 'delta' in df.columns

            # Check that delta value is present
            assert df['delta'].iloc[0] == 0.45

    def test_missing_delta_columns_handling(self, mock_config):
        """Test handling of option chain data without delta columns."""
        scanner = OptionsScanner(mock_config)
        
        # Create option chain without delta columns
        option_chain_no_delta = [
            {
                'call_symbol': 'NSE:NIFTY25JUL24000CE',
                'put_symbol': 'NSE:NIFTY25JUL24000PE',
                'strike': 24000,
                'spot_price': 24100
            }
        ]
        
        mock_fyers_client = Mock()
        
        # Should return empty list when delta columns are missing
        filtered_symbols = scanner._apply_delta_based_filtering(option_chain_no_delta, mock_fyers_client)
        
        assert len(filtered_symbols) == 0

    def test_invalid_delta_values_handling(self, mock_config):
        """Test handling of invalid delta values."""
        scanner = OptionsScanner(mock_config)
        
        # Create option chain with invalid delta values
        option_chain_invalid_delta = [
            {
                'call_symbol': 'NSE:NIFTY25JUL24000CE',
                'put_symbol': 'NSE:NIFTY25JUL24000PE',
                'call_delta': 'invalid',
                'put_delta': None,
                'strike': 24000,
                'spot_price': 24100
            }
        ]
        
        mock_fyers_client = Mock()
        
        # Should handle invalid delta values gracefully
        filtered_symbols = scanner._apply_delta_based_filtering(option_chain_invalid_delta, mock_fyers_client)
        
        # Check that delta map handles invalid values
        if hasattr(scanner, '_delta_map'):
            for symbol, delta in scanner._delta_map.items():
                # Invalid deltas should be converted to empty string
                assert isinstance(delta, (float, str))

    @patch('market_type_scanner.logger')
    def test_delta_filtering_error_logging(self, mock_logger, mock_config):
        """Test that delta filtering errors are properly logged."""
        scanner = OptionsScanner(mock_config)
        
        # Create malformed option chain that will cause errors
        malformed_option_chain = "not a list"
        
        mock_fyers_client = Mock()
        
        # Should handle errors gracefully and log them
        filtered_symbols = scanner._apply_delta_based_filtering(malformed_option_chain, mock_fyers_client)
        
        assert len(filtered_symbols) == 0
        # Check that error was logged
        mock_logger.error.assert_called()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
