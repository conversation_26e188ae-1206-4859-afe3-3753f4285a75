# Product Requirements Document (PRD)
## Multi-Market Scanner for NSE Trading

### Version: 1.0
### Date: 23 JULY 2025
### Status: Active Development

---

## 1. Executive Summary

The Multi-Market Scanner is a comprehensive Python-based financial analysis application designed for scanning and analyzing multiple market types from the National Stock Exchange (NSE) of India. The system provides intelligent filtering, real-time market data integration, technical analysis, and automated report generation for EQUITY, INDEX, FUTURES, and OPTIONS instruments.

### 1.1 Key Value Propositions
- **Unified Multi-Market Analysis**: Single platform for analyzing all NSE market segments
- **Intelligent Options Filtering**: Advanced pre-filtering reduces processing time by 70-75%
- **Real-Time Market Integration**: Live data through Fyers API v3 with rate limiting
- **Technical Analysis Integration**: Moving Average Exponential (MAE) and Pivot Point calculations
- **Automated Report Generation**: Market-specific CSV and summary reports
- **High Performance**: Optimized for large datasets with batch processing

---

## 2. Product Overview

### 2.1 Product Vision
To provide traders and analysts with a comprehensive, automated tool for scanning and analyzing NSE market instruments, enabling data-driven trading decisions through intelligent filtering and technical analysis.

### 2.2 Target Users
- **Primary**: Individual traders and small trading firms
- **Secondary**: Financial analysts and researchers
- **Tertiary**: Algorithmic trading system developers

### 2.3 Market Context
- **Market**: Indian equity and derivatives markets (NSE)
- **Instruments**: 73,560+ OPTIONS, thousands of EQUITY/INDEX/FUTURES symbols
- **Data Source**: NSE via Fyers API v3
- **Processing Volume**: Handles large-scale symbol processing efficiently

---

## 3. Functional Requirements

### 3.1 Core Features

#### 3.1.1 Multi-Market Symbol Processing
- **FR-001**: Load and parse symbols from NSE_CM.csv (Cash Market) and NSE_FO.csv (Futures & Options)
- **FR-002**: Support for EQUITY, INDEX, FUTURES, and OPTIONS market types
- **FR-003**: Universal symbol parsing engine for all market types
- **FR-004**: Automatic symbol file downloading from NSE public APIs

#### 3.1.2 Intelligent Options Filtering
- **FR-005**: Pre-filter OPTIONS symbols based on spot prices and strike ranges
- **FR-006**: Dynamic strike interval detection (NIFTY: 50, BANKNIFTY: 100, etc.)
- **FR-007**: Configurable strike level filtering around At-The-Money (ATM)
- **FR-008**: CE/PE pairing with same expiry requirements
- **FR-009**: Reduce symbol processing by 70-75% through intelligent pre-filtering

#### 3.1.3 Market Data Integration
- **FR-010**: Real-time market data fetching via Fyers API v3
- **FR-011**: Batch processing with rate limiting (0.1s delay between requests)
- **FR-012**: Retry logic with exponential backoff (5 retries maximum)
- **FR-013**: Support for multiple timeframes (1min, 5min, 30min, 60min, 1D)
- **FR-014**: Historical OHLC data retrieval for technical analysis

#### 3.1.4 Advanced Filtering System
- **FR-015**: Volume-based filtering (configurable min/max thresholds)
- **FR-016**: Last Traded Price (LTP) range filtering
- **FR-017**: Market-specific filtering rules (INDEX volume exemption)
- **FR-018**: Expiry-based filtering (current + next 2 months for options)
- **FR-019**: Delta-based filtering for options (0.27 to 0.64 range)

#### 3.1.5 Technical Analysis
- **FR-020**: Moving Average Exponential (MAE) calculation using TA library
- **FR-021**: Configurable MAE parameters (length, source, smoothing)
- **FR-022**: Pivot Point calculations (DAILY, WEEKLY, MONTHLY)
- **FR-023**: Support/Resistance level identification (S1-S5, R1-R5)
- **FR-024**: Distance calculations to pivot levels with percentage metrics

#### 3.1.6 Report Generation
- **FR-025**: Market-specific CSV reports with appropriate columns
- **FR-026**: Summary reports with statistics and top performers
- **FR-027**: Combined summary across all market types
- **FR-028**: Timestamped file naming for historical tracking
- **FR-029**: Configurable output directory structure

### 3.2 Configuration Management
- **FR-030**: YAML-based configuration system (config.yaml)
- **FR-031**: Environment-specific settings (.env file support)
- **FR-032**: Runtime configuration validation
- **FR-033**: Market type enable/disable functionality
- **FR-034**: Symbol-specific configuration (ALL, NIFTY50, specific symbols)

### 3.3 Authentication & Security
- **FR-035**: Fyers API v3 authentication with daily token caching
- **FR-036**: Secure credential management via environment variables
- **FR-037**: API rate limiting compliance
- **FR-038**: Error handling for authentication failures

---

## 4. Non-Functional Requirements

### 4.1 Performance Requirements
- **NFR-001**: Process 73,560+ symbols with <3 minutes total execution time
- **NFR-002**: Memory usage optimization for large datasets
- **NFR-003**: Batch processing to handle API rate limits
- **NFR-004**: 70-75% reduction in API calls through pre-filtering
- **NFR-005**: Support for concurrent processing where applicable

### 4.2 Reliability Requirements
- **NFR-006**: 99% uptime during market hours
- **NFR-007**: Graceful error handling with detailed logging
- **NFR-008**: Automatic retry mechanisms for API failures
- **NFR-009**: Data validation and integrity checks
- **NFR-010**: Fallback mechanisms for missing data

### 4.3 Scalability Requirements
- **NFR-011**: Handle increasing symbol counts (future market expansion)
- **NFR-012**: Configurable batch sizes for different system capabilities
- **NFR-013**: Modular architecture for easy feature additions
- **NFR-014**: Support for multiple underlying symbols simultaneously

### 4.4 Usability Requirements
- **NFR-015**: Command-line interface with clear status messages
- **NFR-016**: Comprehensive logging with multiple levels (INFO, DEBUG, ERROR)
- **NFR-017**: Clear error messages and troubleshooting guidance
- **NFR-018**: Configuration validation with helpful error messages

### 4.5 Maintainability Requirements
- **NFR-019**: Modular code architecture with clear separation of concerns
- **NFR-020**: Comprehensive documentation and code comments
- **NFR-021**: Type hints for all function signatures
- **NFR-022**: Standardized coding conventions (PEP 8)
- **NFR-023**: Comprehensive test suite coverage

---

## 5. Technical Architecture

### 5.1 System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   main.py       │    │  config.yaml     │    │   .env          │
│ (Entry Point)   │────│ (Configuration)  │    │ (Credentials)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ unified_scanner │    │ config_loader.py │    │ fyers_config.py │
│      .py        │────│                  │────│                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│market_type_     │    │universal_symbol_ │    │options_chain_   │
│scanner.py       │────│parser.py         │────│filter.py        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ fyers_client.py │    │technical_        │    │pivot_point_     │
│                 │────│indicators.py     │────│integration.py   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌──────────────────┐
│report_generator │    │     reports/     │
│     .py         │────│   (Output Dir)   │
└─────────────────┘    └──────────────────┘
```

### 5.2 Core Components

#### 5.2.1 Application Layer
- **main.py**: Application entry point and orchestration
- **unified_scanner.py**: Central coordinator for all market types
- **config_loader.py**: Configuration management and validation

#### 5.2.2 Data Processing Layer
- **universal_symbol_parser.py**: Multi-market symbol parsing engine
- **market_type_scanner.py**: Market-specific scanning logic
- **options_chain_filter.py**: Intelligent options filtering

#### 5.2.3 Market Data Layer
- **fyers_client.py**: API client with rate limiting and retry logic
- **fyers_config.py**: Authentication and connection management
- **symbol_downloader.py**: Automated symbol file management

#### 5.2.4 Analysis Layer
- **technical_indicators.py**: MAE and other technical calculations
- **pivot_point_integration.py**: Pivot point analysis
- **pivot_points.py**: Core pivot point calculation algorithms

#### 5.2.5 Output Layer
- **report_generator.py**: Multi-format report generation
- **Reports directory**: Timestamped output files

### 5.3 Data Flow

1. **Initialization**: Load configuration and validate prerequisites
2. **Symbol Loading**: Download and parse NSE symbol files
3. **Pre-filtering**: Apply intelligent filtering to reduce symbol count
4. **Authentication**: Establish Fyers API connection
5. **Market Data**: Fetch real-time quotes in batches
6. **Analysis**: Apply technical indicators and pivot point calculations
7. **Filtering**: Apply volume, LTP, and market-specific filters
8. **Report Generation**: Create market-specific CSV and summary reports

---

## 6. Data Requirements

### 6.1 Input Data Sources
- **NSE_CM.csv**: Cash market symbols (EQUITY, INDEX)
- **NSE_FO.csv**: Futures and Options symbols
- **Fyers API v3**: Real-time market data and historical OHLC
- **config.yaml**: User configuration parameters
- **.env**: API credentials and sensitive data

### 6.2 Data Processing
- **Symbol Parsing**: Extract market type, underlying, expiry, strike, option type
- **Market Data**: LTP, volume, OHLC, previous close, change percentage
- **Technical Data**: MAE values, pivot points, support/resistance levels
- **Metadata**: Timestamps, symbol counts, processing statistics

### 6.3 Output Data Formats
- **CSV Reports**: Market-specific columns with numerical data
- **Summary Reports**: Text-based statistics and top performers
- **Log Files**: Detailed processing logs with timestamps
- **Combined Reports**: Cross-market analysis summaries

---

## 7. Integration Requirements

### 7.1 External APIs
- **Fyers API v3**: Primary market data source
  - Authentication: OAuth-based with daily token refresh
  - Rate Limits: 0.1s minimum delay between requests
  - Endpoints: Quotes, Historical data, Symbol details

### 7.2 File System Integration
- **Symbol Files**: Automatic download from NSE public URLs
- **Configuration**: YAML and environment file parsing
- **Output**: Structured directory creation and file management
- **Logging**: File-based logging with rotation

### 7.3 Third-Party Libraries
- **pandas**: Data manipulation and analysis
- **ta**: Technical analysis calculations
- **pyyaml**: Configuration file parsing
- **fyers-apiv3**: Official Fyers API client
- **python-dotenv**: Environment variable management

---

## 8. User Interface Requirements

### 8.1 Command Line Interface
- **Startup Banner**: Application information and feature summary
- **Progress Indicators**: Real-time processing status updates
- **Error Messages**: Clear, actionable error descriptions
- **Completion Summary**: Final statistics and output file locations

### 8.2 Configuration Interface
- **YAML Configuration**: Human-readable parameter settings
- **Validation Messages**: Clear feedback on configuration errors
- **Default Values**: Sensible defaults for all parameters
- **Documentation**: Inline comments explaining each setting

### 8.3 Output Interface
- **CSV Reports**: Excel-compatible format with proper headers
- **Summary Reports**: Human-readable text format
- **Log Files**: Structured logging with timestamps and levels
- **File Naming**: Consistent timestamp-based naming convention

---

## 9. Quality Assurance Requirements

### 9.1 Testing Strategy
- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end workflow validation
- **Performance Tests**: Large dataset processing validation
- **Configuration Tests**: Various configuration scenario testing

### 9.2 Test Coverage Areas
- **Market Type Processing**: All four market types (EQUITY, INDEX, FUTURES, OPTIONS)
- **Filtering Logic**: Volume, LTP, expiry, and technical filters
- **API Integration**: Authentication, rate limiting, error handling
- **Report Generation**: All output formats and edge cases

### 9.3 Quality Metrics
- **Code Coverage**: Minimum 80% test coverage
- **Performance**: Sub-3-minute processing for full symbol set
- **Reliability**: 99% success rate for normal operations
- **Accuracy**: 100% data integrity for processed symbols

---

## 10. Security Requirements

### 10.1 Credential Management
- **Environment Variables**: Secure storage of API credentials
- **Token Caching**: Daily token refresh with secure storage
- **Access Control**: File system permissions for sensitive data
- **Audit Trail**: Logging of authentication events

### 10.2 Data Security
- **API Communication**: HTTPS-only communication with Fyers API
- **Local Storage**: Secure handling of temporary data files
- **Log Sanitization**: No sensitive data in log files
- **Error Handling**: No credential exposure in error messages

---

## 11. Deployment Requirements

### 11.1 System Requirements
- **Python**: Version 3.7 or higher
- **Operating System**: Windows, macOS, Linux compatibility
- **Memory**: Minimum 4GB RAM for large dataset processing
- **Storage**: 1GB free space for symbol files and reports
- **Network**: Stable internet connection for API access

### 11.2 Installation Process
- **Dependencies**: Automated installation via requirements.txt
- **Configuration**: Template config.yaml with documentation
- **Credentials**: .env file setup with Fyers API keys
- **Validation**: Prerequisite checking before first run

### 11.3 Operational Requirements
- **Scheduling**: Support for automated daily/hourly runs
- **Monitoring**: Log file monitoring for error detection
- **Maintenance**: Periodic symbol file updates
- **Backup**: Configuration and output file backup procedures

---

## 12. Future Enhancements

### 12.1 Planned Features
- **Web Interface**: Browser-based configuration and monitoring
- **Database Integration**: Persistent storage for historical analysis
- **Additional Exchanges**: BSE and other exchange support
- **Advanced Analytics**: Machine learning-based predictions
- **Real-time Streaming**: Live market data updates

### 12.2 Scalability Improvements
- **Distributed Processing**: Multi-server deployment capability
- **Cloud Integration**: AWS/Azure deployment options
- **API Optimization**: Parallel API calls where permitted
- **Caching Layer**: Redis-based caching for frequently accessed data

### 12.3 User Experience Enhancements
- **GUI Application**: Desktop application with visual interface
- **Mobile App**: Mobile monitoring and alert capabilities
- **Customizable Dashboards**: User-defined report layouts
- **Alert System**: Email/SMS notifications for significant events

---

## 13. Risk Assessment

### 13.1 Technical Risks
- **API Rate Limits**: Fyers API throttling during high usage
- **Data Quality**: Inconsistent or missing market data
- **Performance Degradation**: Large dataset processing bottlenecks
- **Third-party Dependencies**: Library compatibility issues

### 13.2 Mitigation Strategies
- **Rate Limiting**: Built-in delays and retry mechanisms
- **Data Validation**: Comprehensive input validation and error handling
- **Performance Monitoring**: Continuous performance optimization
- **Dependency Management**: Version pinning and regular updates

### 13.3 Business Risks
- **API Changes**: Fyers API modifications affecting functionality
- **Market Structure Changes**: NSE symbol format modifications
- **Regulatory Changes**: New compliance requirements
- **Competition**: Alternative solutions in the market

---

## 14. Success Metrics

### 14.1 Performance Metrics
- **Processing Time**: <3 minutes for full symbol set processing
- **API Efficiency**: 70-75% reduction in API calls through pre-filtering
- **Memory Usage**: Efficient memory utilization for large datasets
- **Error Rate**: <1% processing errors under normal conditions

### 14.2 User Satisfaction Metrics
- **Ease of Use**: Simple configuration and execution process
- **Report Quality**: Comprehensive and actionable output data
- **Reliability**: Consistent performance across different market conditions
- **Documentation**: Clear and comprehensive user guidance

### 14.3 Business Metrics
- **Adoption Rate**: User base growth and retention
- **Feature Utilization**: Usage patterns across different features
- **Support Requests**: Volume and resolution time for user issues
- **Performance Feedback**: User-reported performance improvements

---

## 15. Conclusion

The Multi-Market Scanner represents a comprehensive solution for NSE market analysis, combining intelligent filtering, real-time data integration, and advanced technical analysis in a single, efficient platform. The system's modular architecture, performance optimizations, and extensive configuration options make it suitable for both individual traders and institutional users.

The product successfully addresses the key challenges of large-scale market data processing while maintaining accuracy and providing actionable insights through automated report generation. With its robust error handling, comprehensive testing, and clear upgrade path, the system is well-positioned for long-term success in the dynamic Indian financial markets.

---

**Document Control:**
- **Version**: 1.0
- **Last Updated**: July 23, 2025
- **Next Review**: Quarterly
- **Owner**: Development Team
- **Stakeholders**: Trading Team, Technical Team, End Users
