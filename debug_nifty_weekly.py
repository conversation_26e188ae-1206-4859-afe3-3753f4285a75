"""
Debug script to analyze NIFTY weekly options filtering issue.
This script will help identify why symbols are being filtered out despite successful pivot calculations.
"""

import logging
import sys
import os
from datetime import datetime, date, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import ConfigLoader
from fyers_client import FyersClient
from pivot_point_integration import PivotPointIntegration, PivotPointData
from pivot_points import _calculate_pivot_standard

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_nifty_weekly_pivot_calculations():
    """Test pivot point calculations for NIFTY weekly options."""
    
    # Expected NIFTY weekly symbols from the log
    test_symbols = [
        'NSE:NIFTY2572425050CE',
        'NSE:NIFTY2572425100CE', 
        'NSE:NIFTY2572425150CE',
        'NSE:NIFTY2572425000PE',
        'NSE:NIFTY2572425050PE',
        'NSE:NIFTY2572425100PE'
    ]
    
    # OHLC data from the log
    ohlc_data = {
        'NSE:NIFTY2572425050CE': {'open': 258.0, 'high': 317.8, 'low': 92.0, 'close': 109.4},
        'NSE:NIFTY2572425100CE': {'open': 250.05, 'high': 282.75, 'low': 74.25, 'close': 88.2},
        'NSE:NIFTY2572425150CE': {'open': 210.0, 'high': 248.65, 'low': 60.0, 'close': 70.8},
        'NSE:NIFTY2572425000PE': {'open': 115.0, 'high': 161.0, 'low': 48.6, 'close': 117.65},
        'NSE:NIFTY2572425050PE': {'open': 132.95, 'high': 189.5, 'low': 60.1, 'close': 142.5},
        'NSE:NIFTY2572425100PE': {'open': 153.65, 'high': 222.0, 'low': 74.4, 'close': 171.3}
    }
    
    print("=== NIFTY Weekly Options Pivot Point Debug ===\n")
    
    for symbol in test_symbols:
        if symbol not in ohlc_data:
            print(f"❌ No OHLC data for {symbol}")
            continue
            
        ohlc = ohlc_data[symbol]
        print(f"🔍 Testing {symbol}")
        print(f"   OHLC: O={ohlc['open']}, H={ohlc['high']}, L={ohlc['low']}, C={ohlc['close']}")
        
        # Calculate pivot points using the same logic as the system
        try:
            pivot_levels = _calculate_pivot_standard(ohlc['high'], ohlc['low'], ohlc['close'])
            print(f"   Pivot Levels: {pivot_levels}")
            
            # Create PivotPointData object
            pivot_data = PivotPointData(pivot_levels=pivot_levels)
            
            # Use close price as LTP for testing (this is what the system would use)
            ltp = ohlc['close']
            print(f"   LTP (using close): {ltp}")
            
            # Test the minimum positive pivot calculation
            test_min_positive_pivot_calculation(ltp, pivot_data)
            
            print(f"   Min Positive Pivot Level: {pivot_data.min_positive_pivot_level}")
            print(f"   Min Positive Pivot Value: {pivot_data.min_positive_pivot_value}")
            print(f"   Distance to Min Positive Pivot: {pivot_data.distance_to_min_positive_pivot}")
            print(f"   Distance %: {pivot_data.distance_to_min_positive_pivot_pct}")
            
            # Check if this would pass the filtering criteria
            would_pass_filter = (pivot_data.distance_to_min_positive_pivot is not None and 
                               pivot_data.distance_to_min_positive_pivot != float('inf'))
            print(f"   Would pass filter: {'✅ YES' if would_pass_filter else '❌ NO'}")
            
        except Exception as e:
            print(f"   ❌ Error calculating pivots: {e}")
        
        print()

def test_min_positive_pivot_calculation(ltp: float, pivot_data: PivotPointData) -> None:
    """Test the minimum positive pivot calculation logic."""
    
    if ltp <= 0:
        print(f"   ❌ Invalid LTP: {ltp}")
        return
    
    # Define the pivot levels we're interested in (same as in the actual code)
    pivot_levels = ['Pivot', 'R1', 'R2', 'R3', 'R4', 'R5', 'S1', 'S2', 'S3', 'S4', 'S5']
    positive_pivot_values = []
    
    print(f"   Checking positive pivot values:")
    for level in pivot_levels:
        if level in pivot_data.pivot_levels and pivot_data.pivot_levels[level] > 0:
            value = pivot_data.pivot_levels[level]
            positive_pivot_values.append(value)
            print(f"     {level}: {value} ✅")
        elif level in pivot_data.pivot_levels:
            value = pivot_data.pivot_levels[level]
            print(f"     {level}: {value} ❌ (not positive)")
        else:
            print(f"     {level}: Not found ❌")
    
    if not positive_pivot_values:
        print(f"   ❌ No positive pivot values found!")
        return
    
    # Find the minimum positive pivot value
    min_positive_pivot = min(positive_pivot_values)
    distance = abs(ltp - min_positive_pivot)
    
    print(f"   Positive pivot values: {positive_pivot_values}")
    print(f"   Minimum positive pivot: {min_positive_pivot}")
    print(f"   Distance calculation: |{ltp} - {min_positive_pivot}| = {distance}")
    
    # Find which level corresponds to this minimum value
    min_pivot_level = None
    for level in pivot_levels:
        if level in pivot_data.pivot_levels and pivot_data.pivot_levels[level] == min_positive_pivot:
            min_pivot_level = level
            break
    
    # Update the pivot data
    pivot_data.min_positive_pivot_level = min_pivot_level
    pivot_data.min_positive_pivot_value = min_positive_pivot
    pivot_data.distance_to_min_positive_pivot = distance
    pivot_data.distance_to_min_positive_pivot_pct = (distance / min_positive_pivot * 100) if min_positive_pivot > 0 else 0

def test_filtered_symbol_pivot_data_transfer():
    """Test if pivot data is properly transferred to FilteredSymbol objects."""
    print("\n=== Testing FilteredSymbol Pivot Data Transfer ===\n")

    # Import required classes
    from market_type_scanner import MarketData, FilteredSymbol
    from universal_symbol_parser import UniversalSymbol

    # Create a test MarketData object with pivot data
    test_symbol = 'NSE:NIFTY2572425050CE'
    ohlc = {'open': 258.0, 'high': 317.8, 'low': 92.0, 'close': 109.4}

    # Create MarketData
    market_data = MarketData(
        symbol=test_symbol,
        ltp=ohlc['close'],
        volume=1000,
        open_price=ohlc['open'],
        high_price=ohlc['high'],
        low_price=ohlc['low'],
        close_price=ohlc['close']
    )

    # Calculate and attach pivot data
    pivot_levels = _calculate_pivot_standard(ohlc['high'], ohlc['low'], ohlc['close'])
    pivot_data = PivotPointData(pivot_levels=pivot_levels)
    test_min_positive_pivot_calculation(ohlc['close'], pivot_data)

    # Attach pivot data to market data (simulating apply_pivot_point_filter)
    market_data.pivot_data = pivot_data
    print(f"✅ Attached pivot data to MarketData: {hasattr(market_data, 'pivot_data')}")
    print(f"   Distance to min positive pivot: {market_data.pivot_data.distance_to_min_positive_pivot}")

    # Create a mock UniversalSymbol for parsing
    parsed_symbol = UniversalSymbol(
        symbol='NIFTY2572425050CE',
        market_type='OPTIONS',
        underlying='NIFTY',
        expiry_year='25',
        expiry_month='07_24',  # Weekly format
        strike_price=25050.0,
        option_type='CE'
    )

    # Create FilteredSymbol (simulating convert_to_filtered_symbols)
    filtered_symbol = FilteredSymbol(
        symbol=test_symbol,
        underlying=parsed_symbol.underlying,
        market_type='OPTIONS',
        market_data=market_data,
        expiry_year=parsed_symbol.expiry_year,
        expiry_month=parsed_symbol.expiry_month,
        strike_price=parsed_symbol.strike_price,
        option_type=parsed_symbol.option_type
    )

    # Transfer pivot data (simulating the transfer logic)
    if hasattr(market_data, 'pivot_data'):
        filtered_symbol.pivot_data = market_data.pivot_data
        print(f"✅ Transferred pivot data to FilteredSymbol: {hasattr(filtered_symbol, 'pivot_data')}")
    else:
        print(f"❌ No pivot data found in MarketData")

    # Test the pivot_data_map building logic (simulating the filtering step)
    filtered_symbols = [filtered_symbol]
    pivot_data_map = {}

    for symbol_obj in filtered_symbols:
        print(f"   Checking symbol: {symbol_obj.symbol}")
        print(f"   Has pivot_data attribute: {hasattr(symbol_obj, 'pivot_data')}")
        if hasattr(symbol_obj, 'pivot_data'):
            print(f"   Pivot data is not None: {symbol_obj.pivot_data is not None}")
            if symbol_obj.pivot_data:
                print(f"   Distance to min positive pivot: {symbol_obj.pivot_data.distance_to_min_positive_pivot}")
                pivot_data_map[symbol_obj.symbol] = symbol_obj.pivot_data

    print(f"\n📊 Pivot data map size: {len(pivot_data_map)}")
    print(f"   Expected: 1, Actual: {len(pivot_data_map)}")

    if pivot_data_map:
        print("✅ Pivot data map successfully built - symbols would pass to filtering")
    else:
        print("❌ Pivot data map is empty - symbols would be filtered out!")

    return len(pivot_data_map) > 0

def test_weekly_symbol_parsing_patterns():
    """Test different regex patterns for weekly NIFTY options."""
    print("\n=== Testing Weekly Symbol Parsing Patterns ===\n")

    import re

    test_symbols = [
        'NIFTY2572425050CE',  # July 24, 2025
        'NIFTY2572425100CE',
        'NIFTY2572425000PE',
    ]

    # Current pattern from the code
    current_pattern = re.compile(r'^([A-Z0-9&]+)(\d{2})(0?[1-9]|1[0-2])(\d{2})(\d+(?:\.\d+)?)(CE|PE)$')

    # Alternative patterns to try
    patterns = {
        'old_weekly': re.compile(r'^([A-Z0-9&]+)(\d{2})(0?[1-9]|1[0-2])(\d{2})(\d+(?:\.\d+)?)(CE|PE)$'),
        'new_weekly_v1': re.compile(r'^([A-Z0-9&]+)(\d{2})([1-9]\d{2})(\d+(?:\.\d+)?)(CE|PE)$'),  # Original updated pattern
        'new_weekly_v2': re.compile(r'^([A-Z0-9&]+)(\d{2})([1-9]\d{2})(\d{4,6})(CE|PE)$'),  # Strike 4-6 digits
        'nifty_specific': re.compile(r'^(NIFTY)(\d{2})(\d{3})(\d{5})(CE|PE)$'),  # NIFTY specific: NIFTY + YY + MDD + SSSSS + CE/PE
        'mmdd_combined': re.compile(r'^([A-Z0-9&]+)(\d{2})(\d{3})(\d+(?:\.\d+)?)(CE|PE)$'),  # YY + MDD format
        'flexible_date': re.compile(r'^([A-Z0-9&]+)(\d{2})(\d{2,4})(\d+(?:\.\d+)?)(CE|PE)$'),  # YY + flexible date
        'simple_weekly': re.compile(r'^([A-Z0-9&]+)(\d{5})(\d+(?:\.\d+)?)(CE|PE)$'),  # YY+MDD as one group
    }

    for symbol in test_symbols:
        print(f"Testing symbol: {symbol}")

        for pattern_name, pattern in patterns.items():
            match = pattern.match(symbol)
            if match:
                print(f"  ✅ {pattern_name}: {match.groups()}")

                if pattern_name in ['mmdd_combined', 'new_weekly_v1', 'new_weekly_v2', 'nifty_specific']:
                    underlying, year, date_part, strike, option_type = match.groups()
                    print(f"     Underlying: {underlying}")
                    print(f"     Year: {year}")
                    print(f"     Date part: {date_part} (should be 724 for July 24)")
                    print(f"     Strike: {strike}")
                    print(f"     Option type: {option_type}")

                    # Parse date part (MDD format where M=month, DD=day)
                    if len(date_part) == 3:
                        month = date_part[0]  # First digit is month
                        day = date_part[1:3]  # Last two digits are day
                        print(f"     Parsed: Month={month}, Day={day}")

                        # Convert month to full format
                        month_full = f"0{month}" if len(month) == 1 else month
                        print(f"     Full date: 20{year}-{month_full}-{day} (should be 2025-07-24)")

                        # Verify this is correct
                        if underlying == 'NIFTY' and year == '25' and date_part == '724' and strike == '25050':
                            print(f"     ✅ CORRECT PARSING!")
                        else:
                            print(f"     ❌ Incorrect parsing")

            else:
                print(f"  ❌ {pattern_name}: No match")
        print()

if __name__ == "__main__":
    test_nifty_weekly_pivot_calculations()
    success = test_filtered_symbol_pivot_data_transfer()

    if not success:
        print("\n🚨 ISSUE IDENTIFIED: Pivot data is not being properly transferred to FilteredSymbol objects!")
    else:
        print("\n✅ Pivot data transfer appears to be working correctly.")

    # Test symbol parsing patterns
    test_weekly_symbol_parsing_patterns()
