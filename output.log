
    =========================================================================
                            UNIFIED SCANNER
                            ---------------
      > Scans EQUITY & INDEX symbols from NSE_CM.csv (ending with -EQ and -INDEX)
      > Scans FUTURES & OPTIONS symbols from NSE_FO.csv (monthly options)      
      > Filters by Volume, LTP, LTP Price Pairing CE PE Filter, MAE Indicator 
        based on config.yaml settings.
      > Generates separate CSV and Text reports for each flow.
    =========================================================================
    
All prerequisites satisfied
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-22 23:13:03
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-22 23:13:03
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
2025-07-22 23:13:03,524 - __main__ - INFO - Starting symbol file download process
2025-07-22 23:13:03,534 - __main__ - INFO - Found 2 URLs to download
2025-07-22 23:13:03,537 - __main__ - INFO - Processing URL: https://public.fyers.in/sym_details/NSE_CM.csv -> NSE_CM.csv
2025-07-22 23:13:03,539 - __main__ - INFO - Backed up existing file to mastersymbol\NSE_CM_20250722_231303.csv
2025-07-22 23:13:03,539 - __main__ - INFO - Downloading from https://public.fyers.in/sym_details/NSE_CM.csv...
2025-07-22 23:13:04,275 - __main__ - INFO - Successfully downloaded to NSE_CM.csv
2025-07-22 23:13:04,291 - __main__ - ERROR - Error processing URL https://public.fyers.in/sym_details/NSE_CM.csv: 'charmap' codec can't encode character '\u2713' in position 0: character maps to <undefined>
2025-07-22 23:13:04,291 - __main__ - INFO - Processing URL: https://public.fyers.in/sym_details/NSE_FO.csv -> NSE_FO.csv
2025-07-22 23:13:04,293 - __main__ - INFO - Backed up existing file to mastersymbol\NSE_FO_20250722_231304.csv
2025-07-22 23:13:04,293 - __main__ - INFO - Downloading from https://public.fyers.in/sym_details/NSE_FO.csv...
2025-07-22 23:13:05,604 - __main__ - INFO - Successfully downloaded to NSE_FO.csv
2025-07-22 23:13:05,608 - __main__ - ERROR - Error processing URL https://public.fyers.in/sym_details/NSE_FO.csv: 'charmap' codec can't encode character '\u2713' in position 0: character maps to <undefined>
2025-07-22 23:13:05,608 - __main__ - INFO - Download complete: 2/4 successful
2025-07-22 23:13:05,608 - __main__ - ERROR - Symbol download failed: 'charmap' codec can't encode character '\u2713' in position 2: character maps to <undefined>
Downloading symbol files from 2 sources...
Backed up existing file to mastersymbol\NSE_CM_20250722_231303.csv
Backed up existing file to mastersymbol\NSE_FO_20250722_231304.csv

Download Summary:
  Successful: 2/4
Error: 'charmap' codec can't encode character '\u2713' in position 2: character maps to <undefined>
unified_scanner - ERROR - Failed to download symbol files: Command '['python', 'symbol_downloader.py']' returned non-zero exit status 1.
