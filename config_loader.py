"""
Configuration loader for the multiple market type scanner application.
Loads configuration from config.yaml and provides centralized access to all settings.
"""

import yaml
import os
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class ConfigLoader:
    """Centralized configuration loader for the application."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the configuration loader.
        
        Args:
            config_path: Path to the config.yaml file
        """
        self.config_path = config_path
        self._config = None
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config = yaml.safe_load(file)
            
            logger.info(f"Configuration loaded successfully from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the configuration value (e.g., 'general.env_path')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        if self._config is None:
            return default
        
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    # General Settings
    @property
    def env_path(self) -> str:
        """Get environment file path."""
        return self.get('general.env_path', 'C:/Users/<USER>/Desktop/Python/.env')
    
    @property
    def output_dir(self) -> str:
        """Get output directory for reports."""
        return self.get('general.output_dir', 'reports')

    @property
    def fyers_api_url(self) -> List[str]:
        """Get Fyers API URLs as a list."""
        urls = self.get('general.fyers_api_url', ['https://public.fyers.in/sym_details/NSE_FO.csv'])
        # Ensure it's always a list
        if isinstance(urls, str):
            return [urls]
        return urls

    # Market Types
    @property
    def market_types(self) -> List[str]:
        """Get list of market types to process."""
        return self.get('market_types', ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'])

    # Trading Symbols
    @property
    def symbols(self) -> List[str]:
        """Get list of trading symbols."""
        return self.get('symbols', ['NIFTY', 'BANKNIFTY'])

    def get_target_symbols(self) -> List[str]:
        """Get list of target symbols to scan."""
        return self.get('symbols', [])
    
    # Market Filter Settings
    @property
    def min_volume(self) -> int:
        """Get minimum trading volume filter."""
        return self.get('market_filters.min_volume', 11)

    @property
    def max_volume(self) -> int:
        """Get maximum trading volume filter."""
        return self.get('market_filters.max_volume', ***********)

    @property
    def min_ltp_price(self) -> float:
        """Get minimum LTP price filter."""
        return self.get('market_filters.min_ltp_price', 0.40)

    @property
    def max_ltp_price(self) -> float:
        """Get maximum LTP price filter."""
        return self.get('market_filters.max_ltp_price', 100000.0)

    # Options Filter Settings
    @property
    def options_strike_level(self) -> int:
        """Get options strike level from ATM for both CE and PE."""
        return self.get('options_filter.strike_level', 50)

    @property
    def options_target_months(self) -> List[str]:
        """Get target months for options filtering. Returns empty list if not configured."""
        return self.get('options_filter.target_months', [])

    @property
    def options_expiry_type(self) -> str:
        """Get options expiry type. Returns 'MONTHLY' if not configured or commented out."""
        return self.get('options_filter.expiry_type', 'MONTHLY').upper()

    # Timeframe Settings
    @property
    def timeframe_interval(self) -> int:
        """Get timeframe interval."""
        return self.get('timeframe.interval', 1)
    
    @property
    def days_to_fetch(self) -> int:
        """Get number of days to fetch for OHLC data."""
        return self.get('timeframe.days_to_fetch', 15)

    # MAE Indicator Settings (using ta library)
    @property
    def mae_enabled(self) -> bool:
        """Get whether MAE indicator is enabled."""
        return self.get('mae_indicator.enabled', True)

    @property
    def mae_length(self) -> int:
        """Get MAE length (period)."""
        return self.get('mae_indicator.length', 9)

    @property
    def mae_source(self) -> str:
        """Get MAE source (open, high, low, close, hl2, hlc3, ohlc4)."""
        return self.get('mae_indicator.source', 'close')

    @property
    def mae_offset(self) -> int:
        """Get MAE offset (can be positive or negative)."""
        return self.get('mae_indicator.offset', 0)

    @property
    def mae_smoothing_line(self) -> str:
        """Get MAE smoothing line (sma, ema, wma)."""
        return self.get('mae_indicator.smoothing_line', 'ema')

    @property
    def mae_smoothing_length(self) -> int:
        """Get MAE smoothing length (only positive value)."""
        return self.get('mae_indicator.smoothing_length', 9)

    @property
    def mae_smoothing_enabled(self) -> bool:
        """Get whether MAE smoothing is enabled."""
        return self.get('mae_indicator.smoothing_enabled', False)

    # Pivot Point Indicator Settings
    @property
    def pivot_point_enabled(self) -> bool:
        """Get whether Pivot Point indicator is enabled."""
        return self.get('pivot_point_indicator.enabled', False)

    @property
    def pivot_point_calculation_type(self) -> str:
        """Get Pivot Point calculation type (DAILY, WEEKLY, MONTHLY)."""
        return self.get('pivot_point_indicator.calculation_type', 'WEEKLY')

    @property
    def pivot_point_top_n_closest(self) -> int:
        """Get number of closest pivot points to consider."""
        return self.get('report_filter.top_n_closest', 30)

    @property
    def report_filter_top_n_closest(self) -> int:
        """Get number of closest pivot points to consider from report_filter section."""
        return self.get('report_filter.top_n_closest', 30)

    # Options Filter Settings (Delta)
    @property
    def min_delta(self) -> float:
        """Get minimum delta filter."""
        return self.get('options_filter.min_delta', 0.30)
    
    @property
    def max_delta(self) -> float:
        """Get maximum delta filter."""
        return self.get('options_filter.max_delta', 0.65)
    # CE/PE Pairing Settings
    @property
    def ce_pe_pairing_enabled(self) -> bool:
        """Get whether CE/PE pairing filter is enabled."""
        return self.get('ce_pe_pairing.enabled', False)
    
    @property
    def ce_pe_min_price_percent(self) -> float:
        """Get minimum price percentage for CE/PE pairing."""
        return self.get('ce_pe_pairing.min_price_percent', 0.0)
    
    @property
    def ce_pe_max_price_percent(self) -> float:
        """Get maximum price percentage for CE/PE pairing."""
        return self.get('ce_pe_pairing.max_price_percent', 3.0)
    
    # Rate Limiting Settings
    @property
    def rate_limit_min_delay_seconds(self) -> float:
        """Get minimum delay between API requests (seconds)."""
        return self.get('rate_limit.min_delay_seconds', 1.2)

    @property
    def rate_limit_max_retries(self) -> int:
        """Get maximum number of retries on 429 error."""
        return self.get('rate_limit.max_retries', 3)

    @property
    def rate_limit_retry_backoff(self) -> float:
        """Get additional seconds to wait on each retry."""
        return self.get('rate_limit.retry_backoff', 3)
    # Additional utility methods
    def get_symbol_config(self, symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive configuration for a specific symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with all relevant configuration for the symbol
        """
        return {
            'symbol': symbol,
            'min_volume': self.min_volume,
            'max_volume': self.max_volume,
            'min_price': self.min_ltp_price,
            'max_price': self.max_ltp_price,
            'min_delta': self.min_delta,
            'max_delta': self.max_delta,
            'pivot_calculation_type': self.pivot_point_calculation_type,
            'pivot_top_n_closest': self.pivot_point_top_n_closest,
            'interval': self.timeframe_interval,
            'days_to_fetch': self.days_to_fetch
        }

    def validate_config(self) -> bool:
        """
        Validate the loaded configuration.

        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check required sections exist
            required_sections = ['general', 'symbols', 'market_filters']

            for section in required_sections:
                if section not in self._config:
                    logger.error(f"Missing required configuration section: {section}")
                    return False

            # Validate symbols list is not empty
            if not self.symbols:
                logger.error("No symbols configured")
                return False

            # Validate market types
            market_types = self.get_enabled_market_types()
            if not market_types:
                logger.error("No valid market types configured")
                return False

            # Validate positive values
            if self.min_volume < 0:
                logger.error("min_volume must be non-negative")
                return False

            if self.min_ltp_price < 0:
                logger.error("min_ltp_price must be non-negative")
                return False

            if self.max_ltp_price < 0:
                logger.error("max_ltp_price must be non-negative")
                return False

            if self.min_ltp_price >= self.max_ltp_price:
                logger.error("min_ltp_price must be less than max_ltp_price")
                return False

            # Validate delta range
            if self.min_delta >= self.max_delta:
                logger.error("min_delta must be less than max_delta")
                return False

            # Validate mutual exclusivity of MAE and Pivot Point indicators
            if self.mae_enabled and self.pivot_point_enabled:
                logger.error("mae_indicator and pivot_point_indicator cannot both be enabled. They are mutually exclusive.")
                return False

            # Validate pivot point calculation type if enabled
            if self.pivot_point_enabled:
                valid_calc_types = ['DAILY', 'WEEKLY', 'MONTHLY']
                if self.pivot_point_calculation_type not in valid_calc_types:
                    logger.error(f"pivot_point_calculation_type must be one of {valid_calc_types}")
                    return False

                if self.pivot_point_top_n_closest <= 0:
                    logger.error("pivot_point_top_n_closest must be a positive integer")
                    return False

            logger.info("Configuration validation successful")
            return True

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

    def get_csv_file_for_market_type(self, market_type: str) -> str:
        """
        Get the CSV file that contains symbols for the given market type.

        Args:
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)

        Returns:
            CSV filename for the market type
        """
        if market_type in ['EQUITY', 'INDEX']:
            return 'NSE_CM.csv'
        elif market_type in ['FUTURES', 'OPTIONS']:
            return 'NSE_FO.csv'
        else:
            raise ValueError(f"Unknown market type: {market_type}")

    def get_market_types_for_csv(self, csv_file: str) -> List[str]:
        """
        Get the market types that are contained in the given CSV file.

        Args:
            csv_file: CSV filename

        Returns:
            List of market types in the CSV file
        """
        if csv_file == 'NSE_CM.csv':
            return ['EQUITY', 'INDEX']
        elif csv_file == 'NSE_FO.csv':
            return ['FUTURES', 'OPTIONS']
        else:
            return []

    def get_enabled_market_types(self) -> List[str]:
        """
        Get the list of enabled market types from configuration.

        Returns:
            List of enabled market types
        """
        configured_types = self.market_types
        # Filter to only include valid market types
        valid_types = ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
        return [mt for mt in configured_types if mt in valid_types]

# Global configuration instance
_config_instance = None

def get_config(config_path: str = "config.yaml") -> ConfigLoader:
    """
    Get the global configuration instance.
    
    Args:
        config_path: Path to the config.yaml file
        
    Returns:
        ConfigLoader instance
    """
    global _config_instance
    if _config_instance is None:
        # Use absolute path if relative path is provided
        if not os.path.isabs(config_path):
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_path)
        _config_instance = ConfigLoader(config_path)
    return _config_instance

def reload_config(config_path: str = "config.yaml") -> ConfigLoader:
    """
    Reload the configuration.
    
    Args:
        config_path: Path to the config.yaml file
        
    Returns:
        New ConfigLoader instance
    """
    global _config_instance
    if not os.path.isabs(config_path):
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_path)
    _config_instance = ConfigLoader(config_path)
    return _config_instance
