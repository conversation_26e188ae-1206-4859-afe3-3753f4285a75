"""
Test script to validate all market type combinations work correctly.
This script tests EQUITY, INDEX, FUTURES, and OPTIONS individually and in combination.
"""

import os
import sys
import shutil
import tempfile
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Tuple

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unified_scanner import UnifiedScanner


class MarketTypeTester:
    """Test runner for different market type combinations."""
    
    def __init__(self):
        self.test_results = {}
        self.test_configs = {
            'EQUITY_ONLY': 'test/test_config_equity_only.yaml',
            'INDEX_ONLY': 'test/test_config_index_only.yaml', 
            'FUTURES_ONLY': 'test/test_config_futures_only.yaml',
            'OPTIONS_ONLY': 'test/test_config_options_only.yaml',
            'ALL_MARKETS': 'test/test_config_all_markets.yaml'
        }
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def test_market_combination(self, test_name: str, config_file: str) -> Tuple[bool, str, Dict]:
        """
        Test a specific market type combination.
        
        Args:
            test_name: Name of the test
            config_file: Path to the configuration file
            
        Returns:
            Tuple of (success, error_message, results)
        """
        self.logger.info(f"Testing {test_name} with config: {config_file}")
        
        try:
            # Check if config file exists
            if not os.path.exists(config_file):
                return False, f"Config file not found: {config_file}", {}
            
            # Create temporary output directory for this test
            with tempfile.TemporaryDirectory() as temp_dir:
                # Copy config file to temp directory and modify output path
                temp_config = os.path.join(temp_dir, 'config.yaml')
                
                # Read original config and modify output directory
                with open(config_file, 'r') as f:
                    config_content = f.read()
                
                # Replace output directory with temp directory
                config_content = config_content.replace("output_dir: 'reports'", f"output_dir: '{temp_dir}/reports'")
                
                with open(temp_config, 'w') as f:
                    f.write(config_content)
                
                # Initialize scanner with temp config
                scanner = UnifiedScanner(temp_config)
                
                # Run the scan
                success = scanner.run_unified_scan()
                
                if success:
                    # Collect results
                    results = {
                        'enabled_market_types': scanner.enabled_market_types,
                        'results': {}
                    }
                    
                    for market_type in scanner.enabled_market_types:
                        market_key = market_type.lower()
                        if market_key in scanner.results:
                            market_result = scanner.results[market_key]
                            results['results'][market_type] = {
                                'symbols_found': len(market_result.get('symbols', [])),
                                'summary': market_result.get('summary', {}),
                                'reports_generated': bool(market_result.get('reports', {}))
                            }
                    
                    return True, "", results
                else:
                    return False, "Scanner execution failed", {}
                    
        except Exception as e:
            error_msg = f"Error testing {test_name}: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def run_all_tests(self) -> Dict[str, Dict]:
        """Run all market type combination tests."""
        self.logger.info("Starting comprehensive market type testing...")
        
        all_results = {}
        
        for test_name, config_file in self.test_configs.items():
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"TESTING: {test_name}")
            self.logger.info(f"{'='*60}")
            
            success, error_msg, results = self.test_market_combination(test_name, config_file)
            
            all_results[test_name] = {
                'success': success,
                'error_message': error_msg,
                'results': results
            }
            
            if success:
                self.logger.info(f"✅ {test_name} - PASSED")
                if results.get('results'):
                    for market_type, market_result in results['results'].items():
                        symbols_count = market_result.get('symbols_found', 0)
                        reports_generated = market_result.get('reports_generated', False)
                        self.logger.info(f"   {market_type}: {symbols_count} symbols, Reports: {'✅' if reports_generated else '❌'}")
            else:
                self.logger.error(f"❌ {test_name} - FAILED: {error_msg}")
        
        return all_results

    def generate_test_report(self, results: Dict[str, Dict]) -> str:
        """Generate a comprehensive test report."""
        report_lines = []
        report_lines.append("MARKET TYPE COMBINATION TEST REPORT")
        report_lines.append("=" * 50)
        report_lines.append("")
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r['success'])
        failed_tests = total_tests - passed_tests
        
        report_lines.append(f"SUMMARY:")
        report_lines.append(f"  Total Tests: {total_tests}")
        report_lines.append(f"  Passed: {passed_tests}")
        report_lines.append(f"  Failed: {failed_tests}")
        report_lines.append(f"  Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        report_lines.append("")
        
        report_lines.append("DETAILED RESULTS:")
        report_lines.append("-" * 30)
        
        for test_name, result in results.items():
            status = "PASS" if result['success'] else "FAIL"
            report_lines.append(f"\n{test_name}: {status}")
            
            if result['success'] and result['results'].get('results'):
                for market_type, market_result in result['results']['results'].items():
                    symbols_count = market_result.get('symbols_found', 0)
                    reports_generated = market_result.get('reports_generated', False)
                    report_lines.append(f"  - {market_type}: {symbols_count} symbols, Reports: {'Yes' if reports_generated else 'No'}")
            elif not result['success']:
                report_lines.append(f"  Error: {result['error_message']}")
        
        report_lines.append("")
        report_lines.append("RECOMMENDATIONS:")
        report_lines.append("-" * 20)
        
        if failed_tests == 0:
            report_lines.append("✅ All market type combinations are working correctly!")
        else:
            report_lines.append("❌ Some market type combinations failed. Please review the errors above.")
            
        return "\n".join(report_lines)

    def validate_functionality(self, results: Dict[str, Dict]) -> List[str]:
        """Validate that all expected functionalities are working."""
        issues = []
        
        # Check that each market type can be scanned individually
        individual_tests = ['EQUITY_ONLY', 'INDEX_ONLY', 'FUTURES_ONLY', 'OPTIONS_ONLY']
        for test_name in individual_tests:
            if test_name not in results or not results[test_name]['success']:
                issues.append(f"Individual {test_name.replace('_ONLY', '')} scanning failed")
        
        # Check that all markets can be scanned together
        if 'ALL_MARKETS' not in results or not results['ALL_MARKETS']['success']:
            issues.append("Combined market scanning failed")
        
        # Check that reports are generated
        for test_name, result in results.items():
            if result['success'] and result['results'].get('results'):
                for market_type, market_result in result['results']['results'].items():
                    if not market_result.get('reports_generated', False):
                        issues.append(f"Reports not generated for {market_type} in {test_name}")
        
        return issues


def main():
    """Main test execution function."""
    print("Starting Market Type Combination Testing...")
    print("=" * 60)
    
    tester = MarketTypeTester()
    
    # Run all tests
    results = tester.run_all_tests()
    
    # Generate report
    report = tester.generate_test_report(results)
    print("\n" + report)
    
    # Validate functionality
    issues = tester.validate_functionality(results)
    
    if issues:
        print("\nFUNCTIONALITY ISSUES FOUND:")
        print("-" * 30)
        for issue in issues:
            print(f"❌ {issue}")
        return False
    else:
        print("\n✅ ALL FUNCTIONALITY TESTS PASSED!")
        return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
