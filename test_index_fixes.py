#!/usr/bin/env python3
"""
Test script to validate INDEX market type fixes.
Tests the NSE_CM.csv symbol loading, dynamic symbol mapping, and pivot point calculation.
"""

import logging
import sys
import os

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_nse_cm_symbol_loading():
    """Test NSE_CM.csv symbol loading without headers."""
    try:
        logger.info("Testing NSE_CM.csv symbol loading...")
        
        from fyers_connect import FyersConnect
        from fyers_config import FyersConfig
        
        # Initialize FyersConnect to test index symbol loading
        fyers_config = FyersConfig()
        fyers_connect = FyersConnect(fyers_config)
        
        # Test index symbol loading
        index_symbols = fyers_connect.index_symbols
        logger.info(f"Loaded {len(index_symbols)} INDEX symbols from NSE_CM.csv")
        
        # Test dynamic mapping
        mapping = fyers_connect.get_dynamic_index_symbol_mapping()
        logger.info(f"Created dynamic mapping with {len(mapping)} entries")
        
        # Show some examples
        for i, (key, value) in enumerate(mapping.items()):
            if i < 5:  # Show first 5 mappings
                logger.info(f"  {key} -> {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"NSE_CM.csv symbol loading test failed: {e}")
        return False

def test_index_symbol_resolution():
    """Test INDEX symbol resolution in universal symbol parser."""
    try:
        logger.info("Testing INDEX symbol resolution...")
        
        from config_loader import ConfigLoader
        from universal_symbol_parser import UniversalSymbolParser
        
        # Load configuration
        config = ConfigLoader("config.yaml")
        
        # Test with specific INDEX symbols
        test_symbols = ['NIFTY50', 'NIFTY', 'BANKNIFTY']
        parser = UniversalSymbolParser(config, test_symbols)
        
        # Get INDEX symbols
        index_symbols = parser.get_symbols_for_market_type('INDEX', test_symbols)
        logger.info(f"Found {len(index_symbols)} INDEX symbols for {test_symbols}")
        
        for symbol in index_symbols:
            logger.info(f"  Resolved: {symbol}")
        
        return len(index_symbols) > 0
        
    except Exception as e:
        logger.error(f"INDEX symbol resolution test failed: {e}")
        return False

def test_index_pivot_point_calculation():
    """Test INDEX pivot point calculation."""
    try:
        logger.info("Testing INDEX pivot point calculation...")

        from config_loader import ConfigLoader
        from fyers_client import FyersClient
        from pivot_point_integration import PivotPointIntegration

        # Load configuration
        config = ConfigLoader("config.yaml")

        # Initialize components (FyersClient expects env_path string)
        fyers_client = FyersClient(env_path="../.env")
        pivot_integration = PivotPointIntegration(config, fyers_client)
        
        # Test with a sample INDEX symbol
        test_symbol = "NSE:NIFTY50-INDEX"
        
        # Create mock market data
        class MockMarketData:
            def __init__(self):
                self.ltp = 24500.0
        
        mock_data = MockMarketData()
        
        # Test pivot point calculation
        pivot_data = pivot_integration.calculate_pivot_points_for_symbol(test_symbol, mock_data)
        
        if pivot_data:
            logger.info(f"Pivot point calculation successful for {test_symbol}")
            logger.info(f"  Pivot levels: {len(pivot_data.pivot_levels) if pivot_data.pivot_levels else 0}")
            return True
        else:
            logger.warning(f"Pivot point calculation returned None for {test_symbol}")
            return False
        
    except Exception as e:
        logger.error(f"INDEX pivot point calculation test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("=" * 60)
    logger.info("TESTING INDEX MARKET TYPE FIXES")
    logger.info("=" * 60)
    
    tests = [
        ("NSE_CM.csv Symbol Loading", test_nse_cm_symbol_loading),
        ("INDEX Symbol Resolution", test_index_symbol_resolution),
        ("INDEX Pivot Point Calculation", test_index_pivot_point_calculation),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            logger.error(f"{test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("All tests PASSED! INDEX fixes are working correctly.")
        return 0
    else:
        logger.error("Some tests FAILED. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
