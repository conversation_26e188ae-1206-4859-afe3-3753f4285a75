root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 00:57:27
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 00:57:27
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
unified_scanner - INFO - Application interrupted by user
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 00:59:04
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 00:59:04
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\simple_hft
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['MCX', 'BSE']
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8235.0, K=7300.0, T=0.0273972602739726, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7600CE, price=604.9, S=8235.0, K=7600.0, T=0.0273972602739726, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7700CE, price=491.1, S=8235.0, K=7700.0, T=0.0273972602739726, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Delta-based filtering: Created delta map with 9 entries
market_type_scanner - INFO - Pivot point mode: Added 9 options for MCX
root - INFO - Generating config for new symbol 'BSE' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:BSE25JUL3200PE, price=665.0, S=2448.5, K=3200.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:BSE25JUL3300PE, price=751.0, S=2448.5, K=3300.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Delta-based filtering: Created delta map with 4 entries
market_type_scanner - INFO - Pivot point mode: Added 4 options for BSE
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 13
market_type_scanner - INFO - Pivot point mode: Fetching market data for 13 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 13 symbols
fyers_client - INFO - Quote fetching progress: 13/13 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 13/13 symbols (100.0% batch success rate) in 0.7s
market_type_scanner - INFO - Volume filter: 13/13 symbols passed
market_type_scanner - INFO - LTP filter: 13/13 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 13 symbols
market_type_scanner - INFO - Pivot point calculation progress: 1/13 symbols (7.7%) - 0 successful calculations
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Pivot point calculation progress: 10/13 symbols (76.9%) - 9 successful calculations
market_type_scanner - INFO - Pivot point calculation progress: 13/13 symbols (100.0%) - 12 successful calculations
market_type_scanner - INFO - Pivot point calculation completed: 13/13 symbols successful (100.0% success rate)
pivot_point_integration - INFO - Filtered to top 13 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 13 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 13 unpaired symbols
report_generator - INFO - Sorted 13 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 13 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 2 underlyings...
fyers_client - INFO - Quote fetching progress: 2/2 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 2/2 underlyings
report_generator - INFO - Fetched spot prices for 2 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250721_010003.csv
report_generator - INFO - Report contains 13 symbols
report_generator - WARNING - Found 13 unpaired symbols
report_generator - INFO - Sorted 13 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 13 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250721_010003.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250721_010003.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250721_010003.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 13
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250721_010003.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250721_010003.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_010004.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 01:00:04
unified_scanner - INFO - Total symbols found: 13
unified_scanner - INFO -   - OPTIONS symbols: 13
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_010004.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 01:01:25
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 01:01:25
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: False, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 615 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 78719 rows, found 615 valid symbols
universal_symbol_parser - INFO - Found 615 OPTIONS symbols for scanning
market_type_scanner - INFO - Loaded 615 raw OPTIONS symbols
market_type_scanner - INFO - Fetching market data for 615 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 615 symbols
fyers_client - INFO - Quote fetching progress: 50/615 symbols (8.1%) - Batch 1/13 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 250/615 symbols (40.7%) - Batch 5/13 - Rate: 4.3 batches/sec, ETA: 2s
fyers_client - INFO - Quote fetching progress: 500/615 symbols (81.3%) - Batch 10/13 - Rate: 2.7 batches/sec, ETA: 1s
fyers_client - INFO - Quote fetching progress: 615/615 symbols (100.0%) - Batch 13/13 - Rate: 2.1 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 615/615 symbols (100.0% batch success rate) in 6.9s
market_type_scanner - INFO - Volume filter: 152/615 symbols passed
market_type_scanner - INFO - LTP filter: 152/152 symbols passed
options_chain_filter - INFO - Target months for options filtering (configured): ['JUL']
options_chain_filter - INFO - Monthly expiry filter: 104/152 options passed
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 2 underlyings...
fyers_client - INFO - Quote fetching progress: 2/2 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 1.3s
options_chain_filter - INFO - Retrieved spot prices for 2/2 underlyings
options_chain_filter - INFO - Fetched spot prices for 2 underlyings
options_chain_filter - INFO - Enhanced options filter: 84/104 options selected
market_type_scanner - INFO - Options chain filter: 84/152 symbols passed
market_type_scanner - INFO - CE/PE pairing filter: 12/84 symbols paired (6 pairs created)
market_type_scanner - INFO - OPTIONS scanning completed: 12 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - Sorted 12 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 6 CE/PE pairs, 0 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 2 underlyings...
fyers_client - INFO - Quote fetching progress: 2/2 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 2/2 underlyings
report_generator - INFO - Fetched spot prices for 2 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250721_010139.csv
report_generator - INFO - Report contains 12 symbols
report_generator - INFO - Sorted 12 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 6 CE/PE pairs, 0 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250721_010139.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250721_010139.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250721_010139.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 12
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250721_010139.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250721_010139.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_010139.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 01:01:39
unified_scanner - INFO - Total symbols found: 12
unified_scanner - INFO -   - OPTIONS symbols: 12
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_010139.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 01:02:38
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 01:02:38
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 3 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: False, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 1157 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 78719 rows, found 1157 valid symbols
universal_symbol_parser - INFO - Found 1157 OPTIONS symbols for scanning
market_type_scanner - INFO - Loaded 1157 raw OPTIONS symbols
market_type_scanner - INFO - Fetching market data for 1157 OPTIONS symbols
market_type_scanner - INFO - Medium symbol set: Using optimized fetching for 1157 symbols
fyers_client - INFO - Processing 1157 symbols in chunks of 1000 for memory optimization
fyers_client - INFO - Chunk processing progress: 1000/1157 symbols (86.4%) - Chunk 1/2 - Rate: 0.0 chunks/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 50/1000 symbols (5.0%) - Batch 1/20 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 250/1000 symbols (25.0%) - Batch 5/20 - Rate: 2.2 batches/sec, ETA: 7s
fyers_client - INFO - Quote fetching progress: 500/1000 symbols (50.0%) - Batch 10/20 - Rate: 1.5 batches/sec, ETA: 7s
fyers_client - INFO - Quote fetching progress: 800/1000 symbols (80.0%) - Batch 16/20 - Rate: 1.8 batches/sec, ETA: 2s
fyers_client - INFO - Quote fetching progress: 1000/1000 symbols (100.0%) - Batch 20/20 - Rate: 1.8 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1000/1000 symbols (100.0% batch success rate) in 11.6s
fyers_client - INFO - Chunk processing progress: 1157/1157 symbols (100.0%) - Chunk 2/2 - Rate: 0.2 chunks/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 50/157 symbols (31.8%) - Batch 1/4 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 100/157 symbols (63.7%) - Batch 2/4 - Rate: 8.7 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 150/157 symbols (95.5%) - Batch 3/4 - Rate: 6.1 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 157/157 symbols (100.0%) - Batch 4/4 - Rate: 3.3 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 157/157 symbols (100.0% batch success rate) in 1.9s
fyers_client - INFO - Completed chunk processing: 1157/1157 symbols retrieved in 13.5s (85.5 symbols/sec)
market_type_scanner - INFO - Volume filter: 289/1157 symbols passed
market_type_scanner - INFO - LTP filter: 274/289 symbols passed
options_chain_filter - INFO - Target months for options filtering (automatic): ['JUL', 'AUG', 'SEP']
options_chain_filter - INFO - Monthly expiry filter: 274/274 options passed
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 3 underlyings...
fyers_client - INFO - Quote fetching progress: 3/3 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 3/3 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 3/3 underlyings
options_chain_filter - INFO - Fetched spot prices for 3 underlyings
options_chain_filter - INFO - Enhanced options filter: 252/274 options selected
market_type_scanner - INFO - Options chain filter: 252/274 symbols passed
market_type_scanner - INFO - CE/PE pairing filter: 42/252 symbols paired (21 pairs created)
market_type_scanner - INFO - OPTIONS scanning completed: 42 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - Sorted 42 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 21 CE/PE pairs, 0 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 3 underlyings...
fyers_client - INFO - Quote fetching progress: 3/3 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 3/3 symbols (100.0% batch success rate) in 0.3s
options_chain_filter - INFO - Retrieved spot prices for 3/3 underlyings
report_generator - INFO - Fetched spot prices for 3 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250721_010256.csv
report_generator - INFO - Report contains 42 symbols
report_generator - INFO - Sorted 42 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 21 CE/PE pairs, 0 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250721_010256.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250721_010256.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250721_010256.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 42
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250721_010256.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250721_010256.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_010256.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 01:02:56
unified_scanner - INFO - Total symbols found: 42
unified_scanner - INFO -   - OPTIONS symbols: 42
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_010256.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 15:38:24
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 15:38:24
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: False, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 238 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 78719 rows, found 238 valid symbols
universal_symbol_parser - INFO - Found 238 OPTIONS symbols for scanning
market_type_scanner - INFO - Loaded 238 raw OPTIONS symbols
market_type_scanner - INFO - Fetching market data for 238 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 238 symbols
fyers_client - INFO - Quote fetching progress: 50/238 symbols (21.0%) - Batch 1/5 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 100/238 symbols (42.0%) - Batch 2/5 - Rate: 6.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 150/238 symbols (63.0%) - Batch 3/5 - Rate: 5.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 200/238 symbols (84.0%) - Batch 4/5 - Rate: 4.4 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 238/238 symbols (100.0%) - Batch 5/5 - Rate: 3.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 238/238 symbols (100.0% batch success rate) in 1.8s
market_type_scanner - INFO - Volume filter: 46/238 symbols passed
market_type_scanner - INFO - LTP filter: 39/46 symbols passed
options_chain_filter - INFO - Target months for options filtering (automatic): ['JUL', 'AUG', 'SEP']
options_chain_filter - INFO - Monthly expiry filter: 39/39 options passed
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.3s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
options_chain_filter - INFO - Fetched spot prices for 1 underlyings
options_chain_filter - INFO - Enhanced options filter: 38/39 options selected
market_type_scanner - INFO - Options chain filter: 38/39 symbols passed
market_type_scanner - INFO - CE/PE pairing filter: 0/38 symbols paired (0 pairs created)
market_type_scanner - INFO - OPTIONS scanning completed: 0 symbols found
unified_scanner - WARNING - No OPTIONS symbols found matching the criteria
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_153830.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 15:38:30
unified_scanner - INFO - Total symbols found: 0
unified_scanner - INFO -   - OPTIONS symbols: 0
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_153830.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 15:39:47
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 15:39:47
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: False, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 238 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 78719 rows, found 238 valid symbols
universal_symbol_parser - INFO - Found 238 OPTIONS symbols for scanning
market_type_scanner - INFO - Loaded 238 raw OPTIONS symbols
market_type_scanner - INFO - Fetching market data for 238 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 238 symbols
fyers_client - INFO - Quote fetching progress: 50/238 symbols (21.0%) - Batch 1/5 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 100/238 symbols (42.0%) - Batch 2/5 - Rate: 8.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 150/238 symbols (63.0%) - Batch 3/5 - Rate: 2.8 batches/sec, ETA: 1s
fyers_client - INFO - Quote fetching progress: 200/238 symbols (84.0%) - Batch 4/5 - Rate: 3.1 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching progress: 238/238 symbols (100.0%) - Batch 5/5 - Rate: 3.3 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 238/238 symbols (100.0% batch success rate) in 1.7s
market_type_scanner - INFO - Volume filter: 46/238 symbols passed
market_type_scanner - INFO - LTP filter: 39/46 symbols passed
options_chain_filter - INFO - Target months for options filtering (configured): ['JUL']
options_chain_filter - INFO - Monthly expiry filter: 27/39 options passed
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
options_chain_filter - INFO - Fetched spot prices for 1 underlyings
options_chain_filter - INFO - Enhanced options filter: 27/27 options selected
market_type_scanner - INFO - Options chain filter: 27/39 symbols passed
market_type_scanner - INFO - CE/PE pairing filter: 0/27 symbols paired (0 pairs created)
market_type_scanner - INFO - OPTIONS scanning completed: 0 symbols found
unified_scanner - WARNING - No OPTIONS symbols found matching the criteria
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_153952.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 15:39:52
unified_scanner - INFO - Total symbols found: 0
unified_scanner - INFO -   - OPTIONS symbols: 0
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_153952.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 15:40:56
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 15:40:56
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['KALYANKJIL']
root - INFO - Generating config for new symbol 'KALYANKJIL' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL460CE, price=99.45, S=591.2, K=460.0, T=0.0273972602739726, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL480CE, price=87.85, S=591.2, K=480.0, T=0.0273972602739726, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL680PE, price=83.4, S=591.2, K=680.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL700PE, price=100.35, S=591.2, K=700.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL720PE, price=119.25, S=591.2, K=720.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL740PE, price=138.55, S=591.2, K=740.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL760PE, price=158.15, S=591.2, K=760.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL780PE, price=177.9, S=591.2, K=780.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:KALYANKJIL25JUL800PE, price=197.8, S=591.2, K=800.0, T=0.0273972602739726, r=0.065, option_type=PE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Delta-based filtering: Created delta map with 3 entries
market_type_scanner - INFO - Pivot point mode: Added 3 options for KALYANKJIL
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 3
market_type_scanner - INFO - Pivot point mode: Fetching market data for 3 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 3 symbols
fyers_client - INFO - Quote fetching progress: 3/3 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 3/3 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 3/3 symbols passed
market_type_scanner - INFO - LTP filter: 3/3 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 3 symbols
market_type_scanner - INFO - Pivot point calculation progress: 1/3 symbols (33.3%) - 0 successful calculations
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Pivot point calculation progress: 3/3 symbols (100.0%) - 2 successful calculations
market_type_scanner - INFO - Pivot point calculation completed: 3/3 symbols successful (100.0% success rate)
pivot_point_integration - INFO - Filtered to top 3 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 3 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 3 unpaired symbols
report_generator - INFO - Sorted 3 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 3 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250721_154102.csv
report_generator - INFO - Report contains 3 symbols
report_generator - WARNING - Found 3 unpaired symbols
report_generator - INFO - Sorted 3 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 3 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250721_154102.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250721_154102.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250721_154102.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 3
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250721_154102.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250721_154102.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_154102.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 15:41:02
unified_scanner - INFO - Total symbols found: 3
unified_scanner - INFO -   - OPTIONS symbols: 3
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_154102.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 16:30:58
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 16:30:58
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['NIFTY50']
root - INFO - Generating config for new symbol 'NIFTY50' based on spot price.
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24100CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24100PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24200CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24200PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24300CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24300PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24400CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24400PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24500CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24500PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24600CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24600PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24700CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24700PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24800CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24800PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24900CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24900PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25000CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25000PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25100CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25100PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25200CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25200PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25300CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25300PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25400CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25400PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25500CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25500PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25600CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25600PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25700CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25700PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25800CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25800PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25900CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25900PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26000CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26000PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26100CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26100PE': No match found
market_type_scanner - WARNING - No option chain data fetched for NIFTY50
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 0
market_type_scanner - WARNING - No OPTIONS symbols found for pivot point scanning
unified_scanner - WARNING - No OPTIONS symbols found matching the criteria
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_163103.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 16:31:03
unified_scanner - INFO - Total symbols found: 0
unified_scanner - INFO -   - OPTIONS symbols: 0
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_163103.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 16:31:13
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 16:31:13
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['NIFTY50']
root - INFO - Generating config for new symbol 'NIFTY50' based on spot price.
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24100CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24100PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24200CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24200PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24300CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24300PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24400CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24400PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24500CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24500PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24600CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24600PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24700CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24700PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24800CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24800PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24900CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL24900PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25000CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25000PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25100CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25100PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25200CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25200PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25300CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25300PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25400CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25400PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25500CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25500PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25600CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25600PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25700CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25700PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25800CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25800PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25900CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL25900PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26000CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26000PE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26100CE': No match found
fyers_connect - WARNING - Could not parse monthly symbol 'NSE:NIFTY5025JUL26100PE': No match found
market_type_scanner - WARNING - No option chain data fetched for NIFTY50
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 0
market_type_scanner - WARNING - No OPTIONS symbols found for pivot point scanning
unified_scanner - WARNING - No OPTIONS symbols found matching the criteria
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_163117.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 16:31:17
unified_scanner - INFO - Total symbols found: 0
unified_scanner - INFO -   - OPTIONS symbols: 0
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_163117.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-21 16:31:36
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-21 16:31:36
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['NIFTY']
market_type_scanner - INFO - Delta-based filtering: Created delta map with 16 entries
market_type_scanner - INFO - Pivot point mode: Added 16 options for NIFTY
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 16
market_type_scanner - INFO - Pivot point mode: Fetching market data for 16 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 16 symbols
fyers_client - INFO - Quote fetching progress: 16/16 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 16/16 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 16/16 symbols passed
market_type_scanner - INFO - LTP filter: 16/16 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 16 symbols
market_type_scanner - INFO - Pivot point calculation progress: 1/16 symbols (6.2%) - 0 successful calculations
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Pivot point calculation progress: 10/16 symbols (62.5%) - 9 successful calculations
market_type_scanner - INFO - Pivot point calculation progress: 16/16 symbols (100.0%) - 15 successful calculations
market_type_scanner - INFO - Pivot point calculation completed: 16/16 symbols successful (100.0% success rate)
pivot_point_integration - INFO - Filtered to top 16 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 16 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 16 unpaired symbols
report_generator - INFO - Sorted 16 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 16 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250721_163151.csv
report_generator - INFO - Report contains 16 symbols
report_generator - WARNING - Found 16 unpaired symbols
report_generator - INFO - Sorted 16 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 16 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250721_163151.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250721_163151.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250721_163151.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 16
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250721_163151.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250721_163151.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250721_163151.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-21 16:31:51
unified_scanner - INFO - Total symbols found: 16
unified_scanner - INFO -   - OPTIONS symbols: 16
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250721_163151.txt
